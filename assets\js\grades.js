// ===== Grades Management JavaScript =====

// Global variables
let gradesData = [];
let currentGradeId = null;
let gradesDataTable = null;

// Grade thresholds
const GRADE_THRESHOLDS = {
    EXCELLENT: 90,
    GOOD: 80,
    AVERAGE: 70,
    POOR: 60
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeGradesPage();
    loadGradesData();
    setupGradeEventListeners();
    initializeGradesDataTable();
    setupGradeCalculation();
});

// ===== Initialization Functions =====
function initializeGradesPage() {
    console.log('تم تحميل صفحة رصد الدرجات');
    
    // Set default exam date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('exam_date').value = today;
    
    // Add animation classes to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 200);
    });
}

function setupGradeEventListeners() {
    // Form submission
    const gradesForm = document.getElementById('gradesForm');
    if (gradesForm) {
        gradesForm.addEventListener('submit', handleGradeFormSubmit);
        gradesForm.addEventListener('reset', handleFormReset);
    }
    
    // Grade input listeners
    const gradeInputs = document.querySelectorAll('.grade-input');
    gradeInputs.forEach(input => {
        input.addEventListener('input', handleGradeInput);
        input.addEventListener('blur', validateGradeInput);
    });
    
    // Search functionality
    const searchInput = document.getElementById('searchGrades');
    if (searchInput) {
        searchInput.addEventListener('input', handleGradeSearch);
    }
    
    // Filter functionality
    const statusFilter = document.getElementById('statusFilter');
    const dateFilter = document.getElementById('dateFilter');
    
    if (statusFilter) {
        statusFilter.addEventListener('change', handleGradeFilter);
    }
    
    if (dateFilter) {
        dateFilter.addEventListener('change', handleGradeFilter);
    }
}

function setupGradeCalculation() {
    const gradeInputs = document.querySelectorAll('.grade-input');
    gradeInputs.forEach(input => {
        input.addEventListener('input', calculateGradeSummary);
    });
}

// ===== Data Management =====
function loadGradesData() {
    // Sample data - in real application, this would come from the server
    gradesData = [
        {
            id: 1,
            personnel_id: 1,
            military_number: '12345678',
            full_name: 'أحمد محمد علي السعيد',
            general_course: 95,
            specialization_course: 88,
            physical_education: 82,
            military_movements: 90,
            total_score: 355,
            average: 88.75,
            grade_status: 'ممتاز',
            promotion_status: 'مؤهل للترقية',
            exam_date: '2024-03-15',
            notes: 'أداء متميز في جميع المواد',
            created_at: '2024-03-15'
        },
        {
            id: 2,
            personnel_id: 2,
            military_number: '87654321',
            full_name: 'محمد عبدالله أحمد الزهراني',
            general_course: 75,
            specialization_course: 78,
            physical_education: 85,
            military_movements: 72,
            total_score: 310,
            average: 77.5,
            grade_status: 'جيد',
            promotion_status: 'مؤهل للترقية',
            exam_date: '2024-03-10',
            notes: 'يحتاج تحسين في حركات المشاة',
            created_at: '2024-03-10'
        }
    ];
    
    updateGradesTable();
}

function initializeGradesDataTable() {
    if ($.fn.DataTable) {
        gradesDataTable = $('#gradesTable').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            responsive: true,
            pageLength: 10,
            order: [[9, 'desc']], // Sort by exam date
            columnDefs: [
                { orderable: false, targets: [10] }, // Actions column
                { className: 'text-center', targets: [2, 3, 4, 5, 6, 7, 8] }
            ]
        });
    }
}

// ===== Grade Calculation =====
function calculateGradeSummary() {
    const generalCourse = parseFloat(document.getElementById('general_course').value) || 0;
    const specializationCourse = parseFloat(document.getElementById('specialization_course').value) || 0;
    const physicalEducation = parseFloat(document.getElementById('physical_education').value) || 0;
    const militaryMovements = parseFloat(document.getElementById('military_movements').value) || 0;
    
    // Calculate totals
    const totalScore = generalCourse + specializationCourse + physicalEducation + militaryMovements;
    const average = totalScore > 0 ? (totalScore / 4).toFixed(2) : 0;
    
    // Update summary display
    document.getElementById('total_score').textContent = totalScore;
    document.getElementById('average_score').textContent = average + '%';
    
    // Determine grade status
    const gradeStatus = getGradeStatus(average);
    const promotionStatus = getPromotionStatus(average);
    
    document.getElementById('grade_status').textContent = gradeStatus.status;
    document.getElementById('grade_status').className = `summary-value text-${gradeStatus.class}`;
    
    document.getElementById('promotion_status').textContent = promotionStatus.status;
    document.getElementById('promotion_status').className = `summary-value text-${promotionStatus.class}`;
    
    // Update grade indicators
    updateGradeIndicator('general_indicator', generalCourse);
    updateGradeIndicator('specialization_indicator', specializationCourse);
    updateGradeIndicator('physical_indicator', physicalEducation);
    updateGradeIndicator('movements_indicator', militaryMovements);
    
    // Add animation to summary card
    const summaryCard = document.querySelector('.grade-summary-card');
    summaryCard.classList.add('grade-success');
    setTimeout(() => {
        summaryCard.classList.remove('grade-success');
    }, 600);
}

function updateGradeIndicator(indicatorId, grade) {
    const indicator = document.getElementById(indicatorId);
    if (!indicator) return;
    
    if (grade === 0 || isNaN(grade)) {
        indicator.textContent = 'لم يتم الإدخال';
        indicator.className = 'grade-indicator empty';
        return;
    }
    
    let status, className;
    
    if (grade >= GRADE_THRESHOLDS.EXCELLENT) {
        status = 'ممتاز';
        className = 'grade-indicator excellent';
    } else if (grade >= GRADE_THRESHOLDS.GOOD) {
        status = 'جيد جداً';
        className = 'grade-indicator good';
    } else if (grade >= GRADE_THRESHOLDS.AVERAGE) {
        status = 'جيد';
        className = 'grade-indicator average';
    } else if (grade >= GRADE_THRESHOLDS.POOR) {
        status = 'مقبول';
        className = 'grade-indicator average';
    } else {
        status = 'راسب';
        className = 'grade-indicator poor';
    }
    
    indicator.textContent = status;
    indicator.className = className;
    indicator.classList.add('grade-animate-in');
}

function getGradeStatus(average) {
    if (average >= GRADE_THRESHOLDS.EXCELLENT) return { status: 'ممتاز', class: 'success' };
    if (average >= GRADE_THRESHOLDS.GOOD) return { status: 'جيد جداً', class: 'info' };
    if (average >= GRADE_THRESHOLDS.AVERAGE) return { status: 'جيد', class: 'primary' };
    if (average >= GRADE_THRESHOLDS.POOR) return { status: 'مقبول', class: 'warning' };
    return { status: 'راسب', class: 'danger' };
}

function getPromotionStatus(average) {
    if (average >= 75) return { status: 'مؤهل للترقية', class: 'success' };
    if (average >= 60) return { status: 'قيد المراجعة', class: 'warning' };
    return { status: 'غير مؤهل', class: 'danger' };
}

// ===== Form Handling =====
function handleGradeFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    const formData = new FormData(form);
    const gradeData = {
        personnel_id: formData.get('personnel_id'),
        general_course: parseFloat(formData.get('general_course')),
        specialization_course: parseFloat(formData.get('specialization_course')),
        physical_education: parseFloat(formData.get('physical_education')),
        military_movements: parseFloat(formData.get('military_movements')),
        exam_date: formData.get('exam_date'),
        notes: formData.get('notes') || ''
    };
    
    // Calculate derived values
    gradeData.total_score = gradeData.general_course + gradeData.specialization_course + 
                           gradeData.physical_education + gradeData.military_movements;
    gradeData.average = (gradeData.total_score / 4).toFixed(2);
    gradeData.grade_status = getGradeStatus(gradeData.average).status;
    gradeData.promotion_status = getPromotionStatus(gradeData.average).status;
    
    // Show loading
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate API call
    setTimeout(() => {
        if (saveGradeData(gradeData)) {
            showToast('تم حفظ درجات الاختبار بنجاح', 'success');
            form.reset();
            form.classList.remove('was-validated');
            resetGradeSummary();
            updateGradesTable();
        } else {
            showToast('حدث خطأ أثناء حفظ الدرجات', 'error');
        }
        hideLoading();
    }, 1500);
}

function handleFormReset() {
    resetGradeSummary();
    clearGradeIndicators();
    
    // Set default exam date
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('exam_date').value = today;
}

function resetGradeSummary() {
    document.getElementById('total_score').textContent = '0';
    document.getElementById('average_score').textContent = '0%';
    document.getElementById('grade_status').textContent = '-';
    document.getElementById('promotion_status').textContent = '-';
    
    // Reset classes
    document.getElementById('grade_status').className = 'summary-value';
    document.getElementById('promotion_status').className = 'summary-value';
}

function clearGradeIndicators() {
    const indicators = ['general_indicator', 'specialization_indicator', 'physical_indicator', 'movements_indicator'];
    indicators.forEach(id => {
        const indicator = document.getElementById(id);
        if (indicator) {
            indicator.textContent = '';
            indicator.className = 'grade-indicator empty';
        }
    });
}

function saveGradeData(data) {
    try {
        // Get personnel info
        const personnelSelect = document.getElementById('personnel_select');
        const selectedOption = personnelSelect.options[personnelSelect.selectedIndex];
        const personnelInfo = selectedOption.text.split(' - ');
        
        // Generate new ID
        const newId = Math.max(...gradesData.map(g => g.id), 0) + 1;
        
        // Add new grade record
        const newGrade = {
            id: newId,
            military_number: personnelInfo[0],
            full_name: personnelInfo[1],
            ...data,
            created_at: new Date().toISOString().split('T')[0]
        };
        
        gradesData.push(newGrade);
        return true;
    } catch (error) {
        console.error('خطأ في حفظ الدرجات:', error);
        return false;
    }
}

// ===== Input Validation =====
function handleGradeInput(e) {
    const input = e.target;
    let value = parseFloat(input.value);
    
    // Validate range
    if (value < 0) {
        input.value = 0;
        value = 0;
    } else if (value > 100) {
        input.value = 100;
        value = 100;
    }
    
    // Update input styling based on grade
    updateInputStyling(input, value);
    
    // Real-time calculation
    calculateGradeSummary();
}

function validateGradeInput(e) {
    const input = e.target;
    const value = parseFloat(input.value);
    
    if (isNaN(value) || value < 0 || value > 100) {
        input.classList.add('is-invalid');
        input.classList.remove('is-valid');
    } else {
        input.classList.add('is-valid');
        input.classList.remove('is-invalid');
    }
}

function updateInputStyling(input, value) {
    // Remove existing grade classes
    input.classList.remove('grade-excellent', 'grade-good', 'grade-average', 'grade-poor');
    
    if (isNaN(value)) return;
    
    if (value >= GRADE_THRESHOLDS.EXCELLENT) {
        input.classList.add('grade-excellent');
    } else if (value >= GRADE_THRESHOLDS.GOOD) {
        input.classList.add('grade-good');
    } else if (value >= GRADE_THRESHOLDS.AVERAGE) {
        input.classList.add('grade-average');
    } else {
        input.classList.add('grade-poor');
    }
}

// ===== Table Management =====
function updateGradesTable() {
    const tableBody = document.getElementById('gradesTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    gradesData.forEach(grade => {
        const row = createGradeRow(grade);
        tableBody.appendChild(row);
    });
    
    // Refresh DataTable if initialized
    if (gradesDataTable) {
        gradesDataTable.destroy();
        initializeGradesDataTable();
    }
}

function createGradeRow(grade) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${grade.military_number}</td>
        <td>${grade.full_name}</td>
        <td>${createGradeBadge(grade.general_course)}</td>
        <td>${createGradeBadge(grade.specialization_course)}</td>
        <td>${createGradeBadge(grade.physical_education)}</td>
        <td>${createGradeBadge(grade.military_movements)}</td>
        <td><strong>${grade.total_score}</strong></td>
        <td><strong>${grade.average}%</strong></td>
        <td>${getGradeStatusBadge(grade.grade_status)}</td>
        <td>${formatDate(grade.exam_date)}</td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="viewGrades(${grade.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="editGrades(${grade.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteGrades(${grade.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    row.classList.add('slide-in-right');
    return row;
}

function createGradeBadge(grade) {
    let badgeClass;
    
    if (grade >= GRADE_THRESHOLDS.EXCELLENT) {
        badgeClass = 'grade-excellent';
    } else if (grade >= GRADE_THRESHOLDS.GOOD) {
        badgeClass = 'grade-good';
    } else if (grade >= GRADE_THRESHOLDS.AVERAGE) {
        badgeClass = 'grade-average';
    } else {
        badgeClass = 'grade-poor';
    }
    
    return `<span class="grade-badge ${badgeClass}">${grade}</span>`;
}

function getGradeStatusBadge(status) {
    const statusMap = {
        'ممتاز': 'bg-excellent',
        'جيد جداً': 'bg-very-good',
        'جيد': 'bg-good',
        'مقبول': 'bg-acceptable',
        'راسب': 'bg-failed'
    };
    
    const badgeClass = statusMap[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass}">${status}</span>`;
}

// ===== Grade Actions =====
function viewGrades(id) {
    const grade = gradesData.find(g => g.id === id);
    if (!grade) {
        showToast('لم يتم العثور على بيانات الدرجات', 'error');
        return;
    }
    
    currentGradeId = id;
    displayGradeDetails(grade);
    
    const modal = new bootstrap.Modal(document.getElementById('gradeModal'));
    modal.show();
}

function editGrades(id) {
    const grade = gradesData.find(g => g.id === id);
    if (!grade) {
        showToast('لم يتم العثور على بيانات الدرجات', 'error');
        return;
    }
    
    // Fill form with grade data
    fillFormWithGradeData(grade);
    
    // Scroll to form
    document.querySelector('#gradesForm').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
    
    showToast('تم تحميل بيانات الدرجات للتعديل', 'success');
}

function deleteGrades(id) {
    const grade = gradesData.find(g => g.id === id);
    if (!grade) {
        showToast('لم يتم العثور على بيانات الدرجات', 'error');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف درجات ${grade.full_name}؟`)) {
        gradesData = gradesData.filter(g => g.id !== id);
        updateGradesTable();
        showToast('تم حذف بيانات الدرجات بنجاح', 'success');
    }
}

function displayGradeDetails(grade) {
    const modalBody = document.getElementById('gradeModalBody');
    modalBody.innerHTML = `
        <div class="grade-detail-grid">
            <div class="grade-detail-item">
                <div class="grade-detail-subject">المقرر العام</div>
                <div class="grade-detail-score">${grade.general_course}</div>
                <div class="grade-detail-status">${getGradeLevel(grade.general_course)}</div>
            </div>
            <div class="grade-detail-item">
                <div class="grade-detail-subject">التخصص</div>
                <div class="grade-detail-score">${grade.specialization_course}</div>
                <div class="grade-detail-status">${getGradeLevel(grade.specialization_course)}</div>
            </div>
            <div class="grade-detail-item">
                <div class="grade-detail-subject">التربية البدنية</div>
                <div class="grade-detail-score">${grade.physical_education}</div>
                <div class="grade-detail-status">${getGradeLevel(grade.physical_education)}</div>
            </div>
            <div class="grade-detail-item">
                <div class="grade-detail-subject">حركات المشاة</div>
                <div class="grade-detail-score">${grade.military_movements}</div>
                <div class="grade-detail-status">${getGradeLevel(grade.military_movements)}</div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-6">
                <strong>المجموع الكلي:</strong> ${grade.total_score} من 400
            </div>
            <div class="col-md-6">
                <strong>المعدل العام:</strong> ${grade.average}%
            </div>
            <div class="col-md-6">
                <strong>التقدير:</strong> ${getGradeStatusBadge(grade.grade_status)}
            </div>
            <div class="col-md-6">
                <strong>حالة الترقية:</strong> ${getPromotionStatusBadge(grade.promotion_status)}
            </div>
            <div class="col-12 mt-3">
                <strong>تاريخ الاختبار:</strong> ${formatDate(grade.exam_date)}
            </div>
            ${grade.notes ? `
            <div class="col-12 mt-3">
                <strong>الملاحظات:</strong><br>
                ${grade.notes}
            </div>
            ` : ''}
        </div>
    `;
}

function getGradeLevel(grade) {
    if (grade >= GRADE_THRESHOLDS.EXCELLENT) return 'ممتاز';
    if (grade >= GRADE_THRESHOLDS.GOOD) return 'جيد جداً';
    if (grade >= GRADE_THRESHOLDS.AVERAGE) return 'جيد';
    if (grade >= GRADE_THRESHOLDS.POOR) return 'مقبول';
    return 'راسب';
}

function getPromotionStatusBadge(status) {
    const statusMap = {
        'مؤهل للترقية': 'bg-success',
        'قيد المراجعة': 'bg-warning',
        'غير مؤهل': 'bg-danger'
    };
    
    const badgeClass = statusMap[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass}">${status}</span>`;
}

function fillFormWithGradeData(grade) {
    document.getElementById('personnel_select').value = grade.personnel_id;
    document.getElementById('general_course').value = grade.general_course;
    document.getElementById('specialization_course').value = grade.specialization_course;
    document.getElementById('physical_education').value = grade.physical_education;
    document.getElementById('military_movements').value = grade.military_movements;
    document.getElementById('exam_date').value = grade.exam_date;
    document.getElementById('notes').value = grade.notes || '';
    
    // Recalculate summary
    calculateGradeSummary();
}

// ===== Search and Filter =====
function handleGradeSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    
    if (gradesDataTable) {
        gradesDataTable.search(searchTerm).draw();
    }
}

function handleGradeFilter() {
    const statusFilter = document.getElementById('statusFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    
    if (gradesDataTable) {
        // Apply filters
        gradesDataTable.columns(8).search(statusFilter).draw();
        gradesDataTable.columns(9).search(dateFilter).draw();
    }
}

function clearGradeFilters() {
    document.getElementById('searchGrades').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';
    
    if (gradesDataTable) {
        gradesDataTable.search('').columns().search('').draw();
    }
}

// ===== Export Functions =====
function exportGradesData() {
    const csvData = [
        ['الرقم العسكري', 'الاسم', 'المقرر العام', 'التخصص', 'التربية البدنية', 'حركات المشاة', 'المجموع', 'المعدل', 'التقدير', 'تاريخ الاختبار', 'الملاحظات']
    ];
    
    gradesData.forEach(grade => {
        csvData.push([
            grade.military_number,
            grade.full_name,
            grade.general_course,
            grade.specialization_course,
            grade.physical_education,
            grade.military_movements,
            grade.total_score,
            grade.average + '%',
            grade.grade_status,
            grade.exam_date,
            grade.notes || ''
        ]);
    });
    
    exportToCSV(csvData, 'درجات_الاختبارات.csv');
    showToast('تم تصدير البيانات بنجاح', 'success');
}

function printGradesList() {
    window.print();
}

// ===== Utility Functions =====
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function editCurrentGrade() {
    if (currentGradeId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('gradeModal'));
        modal.hide();
        editGrades(currentGradeId);
    }
}
