<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d4af37;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b8941f;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2d4a2b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a2e19;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Outer Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#greenGradient)" stroke="url(#goldGradient)" stroke-width="4"/>
  
  <!-- Inner Circle -->
  <circle cx="100" cy="100" r="75" fill="none" stroke="url(#goldGradient)" stroke-width="2"/>
  
  <!-- Crown -->
  <path d="M70 60 L80 45 L90 55 L100 40 L110 55 L120 45 L130 60 L125 70 L75 70 Z" 
        fill="url(#goldGradient)" stroke="#1a2e19" stroke-width="1"/>
  
  <!-- Shield -->
  <path d="M100 75 L85 85 L85 120 L100 135 L115 120 L115 85 Z" 
        fill="url(#greenGradient)" stroke="url(#goldGradient)" stroke-width="2"/>
  
  <!-- Crossed Swords -->
  <g transform="translate(100,100)">
    <!-- Sword 1 -->
    <g transform="rotate(-45)">
      <rect x="-2" y="-25" width="4" height="35" fill="url(#goldGradient)"/>
      <rect x="-6" y="-30" width="12" height="8" fill="url(#goldGradient)"/>
    </g>
    <!-- Sword 2 -->
    <g transform="rotate(45)">
      <rect x="-2" y="-25" width="4" height="35" fill="url(#goldGradient)"/>
      <rect x="-6" y="-30" width="12" height="8" fill="url(#goldGradient)"/>
    </g>
  </g>
  
  <!-- Stars -->
  <g fill="url(#goldGradient)">
    <polygon points="100,25 102,31 108,31 103,35 105,41 100,37 95,41 97,35 92,31 98,31" />
    <polygon points="40,100 42,106 48,106 43,110 45,116 40,112 35,116 37,110 32,106 38,106" />
    <polygon points="160,100 162,106 168,106 163,110 165,116 160,112 155,116 157,110 152,106 158,106" />
  </g>
  
  <!-- Arabic Text Placeholder -->
  <text x="100" y="170" text-anchor="middle" font-family="Arial" font-size="12" fill="url(#goldGradient)" font-weight="bold">
    وزارة الدفاع
  </text>
  <text x="100" y="185" text-anchor="middle" font-family="Arial" font-size="8" fill="url(#greenGradient)">
    المملكة العربية السعودية
  </text>
</svg>
