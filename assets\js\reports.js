// ===== Reports Page JavaScript =====

// Global variables
let statusChart, unitsChart, gradesChart, ranksChart;

// Sample data - in real application, this would come from the server
const reportData = {
    personnel: [
        { id: 1, name: 'أحمد محمد علي السعيد', rank: 'نقيب', unit: 'القوات البرية', status: 'مؤهل للترقية' },
        { id: 2, name: 'محمد عبدالله أحمد الزهراني', rank: 'ملازم أول', unit: 'الحرس الملكي', status: 'قيد المراجعة' },
        { id: 3, name: 'عبدالرحمن سعد محمد القحطاني', rank: 'رائد', unit: 'القوات الجوية', status: 'مؤهل للترقية' }
    ],
    grades: [
        { personnel_id: 1, general: 95, specialization: 88, physical: 82, movements: 90, average: 88.75 },
        { personnel_id: 2, general: 75, specialization: 78, physical: 85, movements: 72, average: 77.5 },
        { personnel_id: 3, general: 92, specialization: 89, physical: 87, movements: 94, average: 90.5 }
    ],
    statistics: {
        total: 1250,
        eligible: 890,
        pending: 280,
        ineligible: 80
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeReportsPage();
    initializeCharts();
    setupReportEventListeners();
});

// ===== Initialization Functions =====
function initializeReportsPage() {
    console.log('تم تحميل صفحة التقارير');
    
    // Add animation classes to cards
    const cards = document.querySelectorAll('.card, .stat-card, .report-option');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in-up');
        }, index * 100);
    });
    
    // Initialize counters
    initializeCounters();
}

function setupReportEventListeners() {
    // Custom report form
    const customReportForm = document.getElementById('customReportForm');
    if (customReportForm) {
        customReportForm.addEventListener('submit', function(e) {
            e.preventDefault();
            generateCustomReport();
        });
    }
    
    // Set default dates for custom report
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    document.getElementById('dateFrom').value = lastMonth.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
}

// ===== Chart Initialization =====
function initializeCharts() {
    initializeStatusChart();
    initializeUnitsChart();
    initializeGradesChart();
    initializeRanksChart();
}

function initializeStatusChart() {
    const ctx = document.getElementById('statusChart').getContext('2d');
    
    statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['مؤهل للترقية', 'قيد المراجعة', 'غير مؤهل'],
            datasets: [{
                data: [890, 280, 80],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#dc3545'
                ],
                borderWidth: 3,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });
}

function initializeUnitsChart() {
    const ctx = document.getElementById('unitsChart').getContext('2d');
    
    unitsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['الحرس الملكي', 'القوات البرية', 'القوات الجوية', 'القوات البحرية', 'قوات الدفاع الجوي'],
            datasets: [{
                label: 'عدد الأفراد',
                data: [250, 400, 300, 200, 100],
                backgroundColor: [
                    'rgba(45, 74, 43, 0.8)',
                    'rgba(212, 175, 55, 0.8)',
                    'rgba(23, 162, 184, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)'
                ],
                borderColor: [
                    'rgba(45, 74, 43, 1)',
                    'rgba(212, 175, 55, 1)',
                    'rgba(23, 162, 184, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 193, 7, 1)'
                ],
                borderWidth: 2,
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function initializeGradesChart() {
    const ctx = document.getElementById('gradesChart').getContext('2d');
    
    gradesChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['المقرر العام', 'التخصص', 'التربية البدنية', 'حركات المشاة'],
            datasets: [{
                label: 'متوسط الدرجات',
                data: [87.3, 85.1, 84.7, 88.2],
                borderColor: 'rgba(45, 74, 43, 1)',
                backgroundColor: 'rgba(45, 74, 43, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgba(212, 175, 55, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function initializeRanksChart() {
    const ctx = document.getElementById('ranksChart').getContext('2d');
    
    ranksChart = new Chart(ctx, {
        type: 'polarArea',
        data: {
            labels: ['جندي', 'عريف', 'رقيب', 'ملازم', 'نقيب', 'رائد', 'مقدم'],
            datasets: [{
                data: [150, 200, 250, 180, 220, 150, 100],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)',
                    'rgba(45, 74, 43, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo',
                            size: 10
                        }
                    }
                }
            }
        }
    });
}

// ===== Report Generation Functions =====
function generatePersonnelReport(format) {
    showExportProgress('جاري إنشاء تقرير الأفراد...');
    
    setTimeout(() => {
        const data = preparePersonnelData();
        
        switch (format) {
            case 'pdf':
                generatePDFReport('تقرير الأفراد العسكريين', data);
                break;
            case 'excel':
                generateExcelReport('تقرير_الأفراد.xlsx', data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء تقرير الأفراد بنجاح', 'success');
    }, 2000);
}

function generateGradesReport(format) {
    showExportProgress('جاري إنشاء تقرير الدرجات...');
    
    setTimeout(() => {
        const data = prepareGradesData();
        
        switch (format) {
            case 'pdf':
                generatePDFReport('تقرير درجات الاختبارات', data);
                break;
            case 'excel':
                generateExcelReport('تقرير_الدرجات.xlsx', data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء تقرير الدرجات بنجاح', 'success');
    }, 2000);
}

function generateEligibleReport(format) {
    showExportProgress('جاري إنشاء تقرير المؤهلين للترقية...');
    
    setTimeout(() => {
        const data = prepareEligibleData();
        
        switch (format) {
            case 'pdf':
                generatePDFReport('المؤهلون للترقية', data);
                break;
            case 'excel':
                generateExcelReport('المؤهلون_للترقية.xlsx', data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء تقرير المؤهلين للترقية بنجاح', 'success');
    }, 2000);
}

function generateStatisticsReport(format) {
    showExportProgress('جاري إنشاء التقرير الإحصائي...');
    
    setTimeout(() => {
        const data = prepareStatisticsData();
        
        switch (format) {
            case 'pdf':
                generatePDFReport('التقرير الإحصائي', data);
                break;
            case 'excel':
                generateExcelReport('التقرير_الإحصائي.xlsx', data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء التقرير الإحصائي بنجاح', 'success');
    }, 2000);
}

function generateUnitsReport(format) {
    showExportProgress('جاري إنشاء تقرير الوحدات...');
    
    setTimeout(() => {
        const data = prepareUnitsData();
        
        switch (format) {
            case 'pdf':
                generatePDFReport('تقرير الوحدات العسكرية', data);
                break;
            case 'excel':
                generateExcelReport('تقرير_الوحدات.xlsx', data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء تقرير الوحدات بنجاح', 'success');
    }, 2000);
}

function generateCustomReport() {
    const form = document.getElementById('customReportForm');
    const formData = new FormData(form);
    
    const reportType = formData.get('reportType');
    const reportFormat = formData.get('reportFormat');
    const dateFrom = formData.get('dateFrom');
    const dateTo = formData.get('dateTo');
    
    if (!reportType || !reportFormat) {
        showToast('يرجى اختيار نوع التقرير والتنسيق', 'error');
        return;
    }
    
    showExportProgress('جاري إنشاء التقرير المخصص...');
    
    setTimeout(() => {
        const data = prepareCustomData(reportType, dateFrom, dateTo);
        
        switch (reportFormat) {
            case 'pdf':
                generatePDFReport(`تقرير مخصص - ${reportType}`, data);
                break;
            case 'excel':
                generateExcelReport(`تقرير_مخصص_${reportType}.xlsx`, data);
                break;
            case 'csv':
                generateCSVReport(`تقرير_مخصص_${reportType}.csv`, data);
                break;
        }
        
        hideExportProgress();
        showToast('تم إنشاء التقرير المخصص بنجاح', 'success');

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('customReportModal'));
        modal.hide();
    }, 2000);
}

// ===== Data Preparation Functions =====
function preparePersonnelData() {
    return [
        ['الرقم العسكري', 'الاسم الكامل', 'الرتبة', 'التخصص', 'الوحدة', 'الحالة'],
        ['12345678', 'أحمد محمد علي السعيد', 'نقيب', 'مشاة', 'القوات البرية', 'مؤهل للترقية'],
        ['87654321', 'محمد عبدالله أحمد الزهراني', 'ملازم أول', 'مدرع', 'الحرس الملكي', 'قيد المراجعة'],
        ['11223344', 'عبدالرحمن سعد محمد القحطاني', 'رائد', 'طيران', 'القوات الجوية', 'مؤهل للترقية']
    ];
}

function prepareGradesData() {
    return [
        ['الرقم العسكري', 'الاسم', 'المقرر العام', 'التخصص', 'التربية البدنية', 'حركات المشاة', 'المعدل'],
        ['12345678', 'أحمد محمد علي السعيد', '95', '88', '82', '90', '88.75%'],
        ['87654321', 'محمد عبدالله أحمد الزهراني', '75', '78', '85', '72', '77.5%'],
        ['11223344', 'عبدالرحمن سعد محمد القحطاني', '92', '89', '87', '94', '90.5%']
    ];
}

function prepareEligibleData() {
    return [
        ['الرقم العسكري', 'الاسم الكامل', 'الرتبة', 'الوحدة', 'المعدل', 'تاريخ الاختبار'],
        ['12345678', 'أحمد محمد علي السعيد', 'نقيب', 'القوات البرية', '88.75%', '2024-03-15'],
        ['11223344', 'عبدالرحمن سعد محمد القحطاني', 'رائد', 'القوات الجوية', '90.5%', '2024-03-20']
    ];
}

function prepareStatisticsData() {
    return [
        ['الإحصائية', 'العدد', 'النسبة'],
        ['إجمالي الأفراد', '1250', '100%'],
        ['المؤهلون للترقية', '890', '71.2%'],
        ['قيد المراجعة', '280', '22.4%'],
        ['غير مؤهلين', '80', '6.4%']
    ];
}

function prepareUnitsData() {
    return [
        ['الوحدة العسكرية', 'عدد الأفراد', 'المؤهلون للترقية', 'النسبة'],
        ['الحرس الملكي', '250', '180', '72%'],
        ['القوات البرية', '400', '290', '72.5%'],
        ['القوات الجوية', '300', '220', '73.3%'],
        ['القوات البحرية', '200', '140', '70%'],
        ['قوات الدفاع الجوي', '100', '60', '60%']
    ];
}

function prepareCustomData(type, dateFrom, dateTo) {
    // Simulate custom data based on type and date range
    switch (type) {
        case 'personnel':
            return preparePersonnelData();
        case 'grades':
            return prepareGradesData();
        case 'units':
            return prepareUnitsData();
        case 'ranks':
            return [
                ['الرتبة', 'العدد', 'المؤهلون للترقية'],
                ['جندي', '150', '100'],
                ['عريف', '200', '150'],
                ['رقيب', '250', '180'],
                ['ملازم', '180', '130'],
                ['نقيب', '220', '160'],
                ['رائد', '150', '110'],
                ['مقدم', '100', '60']
            ];
        default:
            return preparePersonnelData();
    }
}

// ===== Export Functions =====
function generatePDFReport(title, data) {
    // Using jsPDF library
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Add Arabic font support (simplified)
    doc.setFont('helvetica');

    // Header
    doc.setFontSize(20);
    doc.text(title, 105, 20, { align: 'center' });
    doc.text('وزارة الدفاع - المملكة العربية السعودية', 105, 30, { align: 'center' });

    // Date
    const today = new Date().toLocaleDateString('ar-SA');
    doc.setFontSize(12);
    doc.text(`تاريخ التقرير: ${today}`, 20, 50);

    // Table
    let yPosition = 70;
    const lineHeight = 10;

    data.forEach((row, index) => {
        if (index === 0) {
            // Header row
            doc.setFontSize(12);
            doc.setFont('helvetica', 'bold');
        } else {
            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
        }

        let xPosition = 20;
        row.forEach((cell, cellIndex) => {
            doc.text(cell.toString(), xPosition, yPosition);
            xPosition += 30;
        });

        yPosition += lineHeight;

        // Add new page if needed
        if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
        }
    });

    // Footer
    doc.setFontSize(8);
    doc.text('تم إنشاء هذا التقرير بواسطة نظام ترشيحات الترقيات العسكرية', 105, 290, { align: 'center' });

    // Save
    doc.save(`${title}.pdf`);
}

function generateExcelReport(filename, data) {
    // Convert data to CSV format for Excel compatibility
    const csvContent = data.map(row => row.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

function generateCSVReport(filename, data) {
    const csvContent = data.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

// ===== Print Functions =====
function printPersonnelReport() {
    printReport('تقرير الأفراد العسكريين', preparePersonnelData());
}

function printGradesReport() {
    printReport('تقرير درجات الاختبارات', prepareGradesData());
}

function printEligibleReport() {
    printReport('المؤهلون للترقية', prepareEligibleData());
}

function printStatisticsReport() {
    printReport('التقرير الإحصائي', prepareStatisticsData());
}

function printUnitsReport() {
    printReport('تقرير الوحدات العسكرية', prepareUnitsData());
}

function printReport(title, data) {
    const printWindow = window.open('', '_blank');

    const htmlContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .logo { width: 60px; height: 60px; margin: 0 auto 10px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #2d4a2b; color: white; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${title}</h1>
                <h3>وزارة الدفاع - المملكة العربية السعودية</h3>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>

            <table>
                ${data.map((row, index) => `
                    <tr>
                        ${row.map(cell => `<${index === 0 ? 'th' : 'td'}>${cell}</${index === 0 ? 'th' : 'td'}>`).join('')}
                    </tr>
                `).join('')}
            </table>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام ترشيحات الترقيات العسكرية</p>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.print();
}

// ===== UI Helper Functions =====
function showExportProgress(message) {
    const overlay = document.createElement('div');
    overlay.className = 'export-overlay';
    overlay.id = 'exportOverlay';

    const progress = document.createElement('div');
    progress.className = 'export-progress';
    progress.innerHTML = `
        <h5><i class="fas fa-file-export me-2"></i>${message}</h5>
        <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
        </div>
        <small class="text-muted mt-2 d-block">يرجى الانتظار...</small>
    `;

    document.body.appendChild(overlay);
    document.body.appendChild(progress);

    // Animate progress bar
    const progressBar = progress.querySelector('.progress-bar');
    let width = 0;
    const interval = setInterval(() => {
        width += 5;
        progressBar.style.width = width + '%';
        if (width >= 100) {
            clearInterval(interval);
        }
    }, 100);
}

function hideExportProgress() {
    const overlay = document.getElementById('exportOverlay');
    const progress = document.querySelector('.export-progress');

    if (overlay) overlay.remove();
    if (progress) progress.remove();
}

// ===== Counter Animation =====
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number[data-count]');

    const countUp = (element) => {
        const target = parseInt(element.getAttribute('data-count'));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('counted')) {
                entry.target.classList.add('counted');
                countUp(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}
