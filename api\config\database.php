<?php
/**
 * Military Promotion System - Database Configuration
 * نظام ترشيحات الترقيات العسكرية - إعدادات قاعدة البيانات
 */

class Database {
    private $host = "localhost";
    private $db_name = "military_promotion_system";
    private $username = "root";
    private $password = "";
    private $charset = "utf8mb4";
    public $conn;

    /**
     * Get database connection
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }

        return $this->conn;
    }

    /**
     * Close database connection
     * إغلاق اتصال قاعدة البيانات
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Begin transaction
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * Commit transaction
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * Rollback transaction
     * التراجع عن المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * Get last insert ID
     * الحصول على آخر معرف مدرج
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * Execute query with parameters
     * تنفيذ استعلام مع المعاملات
     */
    public function executeQuery($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $exception) {
            error_log("Query error: " . $exception->getMessage());
            throw new Exception("Query execution failed");
        }
    }

    /**
     * Fetch single record
     * جلب سجل واحد
     */
    public function fetchOne($query, $params = []) {
        $stmt = $this->executeQuery($query, $params);
        return $stmt->fetch();
    }

    /**
     * Fetch all records
     * جلب جميع السجلات
     */
    public function fetchAll($query, $params = []) {
        $stmt = $this->executeQuery($query, $params);
        return $stmt->fetchAll();
    }

    /**
     * Insert record
     * إدراج سجل
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->executeQuery($query, $data);
        return $this->lastInsertId();
    }

    /**
     * Update record
     * تحديث سجل
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setClause);
        
        $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->executeQuery($query, $params);
        
        return $stmt->rowCount();
    }

    /**
     * Delete record
     * حذف سجل
     */
    public function delete($table, $where, $whereParams = []) {
        $query = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->executeQuery($query, $whereParams);
        return $stmt->rowCount();
    }

    /**
     * Check if record exists
     * التحقق من وجود سجل
     */
    public function exists($table, $where, $whereParams = []) {
        $query = "SELECT 1 FROM {$table} WHERE {$where} LIMIT 1";
        $result = $this->fetchOne($query, $whereParams);
        return !empty($result);
    }

    /**
     * Count records
     * عد السجلات
     */
    public function count($table, $where = '1=1', $whereParams = []) {
        $query = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetchOne($query, $whereParams);
        return $result['count'] ?? 0;
    }

    /**
     * Get table columns
     * الحصول على أعمدة الجدول
     */
    public function getTableColumns($table) {
        $query = "DESCRIBE {$table}";
        $columns = $this->fetchAll($query);
        return array_column($columns, 'Field');
    }

    /**
     * Sanitize input
     * تنظيف المدخلات
     */
    public function sanitize($input) {
        return htmlspecialchars(strip_tags(trim($input)));
    }

    /**
     * Validate required fields
     * التحقق من الحقول المطلوبة
     */
    public function validateRequired($data, $required) {
        $missing = [];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $missing[] = $field;
            }
        }
        return $missing;
    }

    /**
     * Log audit trail
     * تسجيل مسار التدقيق
     */
    public function logAudit($userId, $action, $tableName, $recordId, $oldValues = null, $newValues = null) {
        $auditData = [
            'user_id' => $userId,
            'action' => $action,
            'table_name' => $tableName,
            'record_id' => $recordId,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ];

        try {
            $this->insert('audit_log', $auditData);
        } catch (Exception $e) {
            error_log("Audit log error: " . $e->getMessage());
        }
    }

    /**
     * Get database statistics
     * الحصول على إحصائيات قاعدة البيانات
     */
    public function getStatistics() {
        $stats = [];
        
        // Personnel statistics
        $stats['total_personnel'] = $this->count('personnel', 'status = ?', ['active']);
        $stats['eligible_personnel'] = $this->count('personnel', 'promotion_eligible = ?', [1]);
        
        // Grades statistics
        $stats['total_exams'] = $this->count('grades');
        $stats['excellent_grades'] = $this->count('grades', 'grade_status = ?', ['excellent']);
        
        // Units statistics
        $stats['total_units'] = $this->count('military_units', 'is_active = ?', [1]);
        
        return $stats;
    }

    /**
     * Backup database
     * نسخ احتياطي لقاعدة البيانات
     */
    public function backup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }

        $command = sprintf(
            'mysqldump --user=%s --password=%s --host=%s %s > %s',
            $this->username,
            $this->password,
            $this->host,
            $this->db_name,
            $filename
        );

        exec($command, $output, $return_var);
        
        return $return_var === 0;
    }

    /**
     * Get connection info
     * معلومات الاتصال
     */
    public function getConnectionInfo() {
        if (!$this->conn) {
            return null;
        }

        return [
            'server_info' => $this->conn->getAttribute(PDO::ATTR_SERVER_INFO),
            'server_version' => $this->conn->getAttribute(PDO::ATTR_SERVER_VERSION),
            'client_version' => $this->conn->getAttribute(PDO::ATTR_CLIENT_VERSION),
            'connection_status' => $this->conn->getAttribute(PDO::ATTR_CONNECTION_STATUS)
        ];
    }
}

/**
 * Database utility functions
 * وظائف مساعدة لقاعدة البيانات
 */
class DatabaseUtils {
    
    /**
     * Generate secure password hash
     * إنشاء تشفير آمن لكلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Verify password
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * Generate unique ID
     * إنشاء معرف فريد
     */
    public static function generateUniqueId($prefix = '') {
        return $prefix . uniqid() . '_' . time();
    }

    /**
     * Format date for database
     * تنسيق التاريخ لقاعدة البيانات
     */
    public static function formatDate($date, $format = 'Y-m-d') {
        if (empty($date)) {
            return null;
        }
        
        $dateObj = DateTime::createFromFormat($format, $date);
        return $dateObj ? $dateObj->format('Y-m-d') : null;
    }

    /**
     * Format datetime for database
     * تنسيق التاريخ والوقت لقاعدة البيانات
     */
    public static function formatDateTime($datetime, $format = 'Y-m-d H:i:s') {
        if (empty($datetime)) {
            return null;
        }
        
        $dateObj = DateTime::createFromFormat($format, $datetime);
        return $dateObj ? $dateObj->format('Y-m-d H:i:s') : null;
    }

    /**
     * Validate email
     * التحقق من البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate military number
     * التحقق من الرقم العسكري
     */
    public static function validateMilitaryNumber($number) {
        return preg_match('/^\d{8}$/', $number);
    }

    /**
     * Validate phone number
     * التحقق من رقم الهاتف
     */
    public static function validatePhone($phone) {
        return preg_match('/^(\+966|0)?[5-9]\d{8}$/', $phone);
    }
}
?>
