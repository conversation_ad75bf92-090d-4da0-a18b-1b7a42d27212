/* ===== Personnel Management Styles ===== */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--military-dark-green) 0%, var(--military-green) 100%);
    color: white;
    padding: 120px 0 60px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="header-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23header-pattern)"/></svg>');
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px var(--shadow-heavy);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.breadcrumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    padding: 0.75rem 1.5rem;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--military-gold);
}

.breadcrumb-item.active {
    color: var(--military-gold);
}

/* Main Content */
.main-content {
    background: var(--military-light-gray);
    min-height: calc(100vh - 200px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    border-bottom: none;
    padding: 1.5rem;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--military-gold), transparent);
}

.card-title {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.card-tools .btn {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.card-tools .btn:hover {
    transform: translateY(-2px);
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.2rem rgba(45, 74, 43, 0.25);
}

.form-control.is-valid,
.form-select.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l-.94-.94L4.5 8.5z'/%3e%3c/svg%3e");
}

.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px var(--shadow-light);
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table thead th {
    background: var(--military-dark-green);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem 0.75rem;
    text-align: center;
    vertical-align: middle;
}

.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(45, 74, 43, 0.05);
    transform: scale(1.01);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    color: #212529 !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 2px;
    padding: 0.375rem 0.5rem;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-light);
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Search and Filter Section */
.input-group-text {
    background: var(--military-green);
    color: white;
    border: 2px solid var(--military-green);
    border-radius: 10px 0 0 10px;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

.input-group .form-control:focus {
    border-color: var(--military-green);
    box-shadow: none;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    border-bottom: none;
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--military-gold), transparent);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 2rem;
}

/* Personnel Details */
.personnel-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.personnel-detail-item:last-child {
    border-bottom: none;
}

.personnel-detail-label {
    font-weight: 600;
    color: var(--military-dark-green);
    flex: 0 0 40%;
}

.personnel-detail-value {
    flex: 1;
    text-align: left;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 15px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--military-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.8rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header {
        padding: 100px 0 40px;
    }
    
    .main-content {
        padding: 2rem 0;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.75rem;
    }
    
    .personnel-detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .personnel-detail-label {
        flex: none;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .page-header,
    .footer,
    .card-header,
    .btn,
    .modal {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th {
        background: #f8f9fa !important;
        color: #212529 !important;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Success/Error States */
.alert-military {
    background: linear-gradient(135deg, var(--military-green), var(--military-dark-green));
    color: white;
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
}

.alert-military .alert-heading {
    color: var(--military-gold);
}
