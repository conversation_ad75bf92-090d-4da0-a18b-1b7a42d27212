/* ===== Reports Page Styles ===== */

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-left: 5px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card-primary {
    border-left-color: #007bff;
}

.stat-card-success {
    border-left-color: #28a745;
}

.stat-card-warning {
    border-left-color: #ffc107;
}

.stat-card-danger {
    border-left-color: #dc3545;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 1rem;
    color: var(--military-gray);
    font-weight: 600;
    margin: 0;
}

/* Report Options */
.report-option {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.report-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--military-green);
}

.report-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.report-option:hover::before {
    transform: scaleX(1);
}

.report-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--military-green), var(--military-dark-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: all 0.3s ease;
    position: relative;
}

.report-option:hover .report-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, var(--military-gold), #b8941f);
}

.report-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s ease;
}

.report-option:hover .report-icon::after {
    transform: translate(-50%, -50%) scale(1);
}

.report-option h5 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--military-dark-green);
    margin-bottom: 1rem;
}

.report-option p {
    color: var(--military-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.report-actions .btn {
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border-width: 2px;
}

.report-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Charts Container */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: none;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--military-gold), transparent);
}

/* Chart Styling */
canvas {
    max-height: 300px !important;
}

/* Section Titles */
.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--military-dark-green);
    position: relative;
    padding-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
    border-radius: 2px;
}

/* Custom Report Modal */
.modal-content {
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    border-bottom: none;
    position: relative;
}

.modal-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--military-gold), transparent);
}

.form-check-input:checked {
    background-color: var(--military-green);
    border-color: var(--military-green);
}

.form-check-input:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.25rem rgba(45, 74, 43, 0.25);
}

/* Loading States */
.report-loading {
    position: relative;
    overflow: hidden;
}

.report-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: reportShimmer 1.5s infinite;
}

@keyframes reportShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Print Styles */
@media print {
    .navbar,
    .page-header,
    .footer,
    .btn,
    .report-actions {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .report-option {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    /* Add header for print */
    .main-content::before {
        content: "تقرير نظام ترشيحات الترقيات العسكرية - وزارة الدفاع";
        display: block;
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #000;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-card {
        padding: 1.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .report-option {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .report-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .report-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .report-actions .btn {
        width: 100%;
        max-width: 200px;
        margin-bottom: 0.5rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
    }
    
    .report-option {
        padding: 1rem;
    }
    
    .report-option h5 {
        font-size: 1.1rem;
    }
    
    .report-option p {
        font-size: 0.9rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Success States */
.report-success {
    animation: reportSuccess 0.6s ease-out;
}

@keyframes reportSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Chart Legends */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--military-gray);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

/* Export Progress */
.export-progress {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    text-align: center;
    min-width: 300px;
}

.export-progress .progress {
    height: 8px;
    border-radius: 10px;
    margin-top: 1rem;
}

.export-progress .progress-bar {
    background: linear-gradient(90deg, var(--military-green), var(--military-gold));
    border-radius: 10px;
}

/* Dark overlay for export progress */
.export-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    backdrop-filter: blur(5px);
}
