// ===== Personnel Management JavaScript =====

// Global variables
let personnelData = [];
let currentPersonnelId = null;
let dataTable = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePersonnelPage();
    loadPersonnelData();
    setupEventListeners();
    initializeDataTable();
});

// ===== Initialization Functions =====
function initializePersonnelPage() {
    console.log('تم تحميل صفحة إدارة الأفراد');
    
    // Add animation classes to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 200);
    });
}

function setupEventListeners() {
    // Form submission
    const personnelForm = document.getElementById('personnelForm');
    if (personnelForm) {
        personnelForm.addEventListener('submit', handleFormSubmit);
    }
    
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // Filter functionality
    const rankFilter = document.getElementById('rankFilter');
    const unitFilter = document.getElementById('unitFilter');
    
    if (rankFilter) {
        rankFilter.addEventListener('change', handleFilter);
    }
    
    if (unitFilter) {
        unitFilter.addEventListener('change', handleFilter);
    }
    
    // Military number formatting
    const militaryNumberInput = document.getElementById('military_number');
    if (militaryNumberInput) {
        militaryNumberInput.addEventListener('input', formatMilitaryNumber);
    }
}

// ===== Data Management =====
function loadPersonnelData() {
    // Sample data - in real application, this would come from the server
    personnelData = [
        {
            id: 1,
            military_number: '12345678',
            full_name: 'أحمد محمد علي السعيد',
            rank: 'نقيب',
            specialization: 'مشاة',
            unit: 'القوات البرية',
            service_years: 12,
            notes: 'أداء ممتاز في التدريبات',
            status: 'مؤهل للترقية',
            created_at: '2024-01-15'
        },
        {
            id: 2,
            military_number: '87654321',
            full_name: 'محمد عبدالله أحمد الزهراني',
            rank: 'ملازم أول',
            specialization: 'مدرع',
            unit: 'الحرس الملكي',
            service_years: 8,
            notes: 'يحتاج تدريب إضافي',
            status: 'قيد المراجعة',
            created_at: '2024-02-10'
        },
        {
            id: 3,
            military_number: '11223344',
            full_name: 'عبدالرحمن سعد محمد القحطاني',
            rank: 'رائد',
            specialization: 'طيران',
            unit: 'القوات الجوية',
            service_years: 15,
            notes: 'خبرة واسعة في الطيران',
            status: 'مؤهل للترقية',
            created_at: '2024-01-20'
        }
    ];
    
    updatePersonnelTable();
}

function initializeDataTable() {
    if ($.fn.DataTable) {
        dataTable = $('#personnelTable').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
            },
            responsive: true,
            pageLength: 10,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [6, 7] }
            ]
        });
    }
}

// ===== Form Handling =====
function handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    const formData = new FormData(form);
    const personnelData = {
        military_number: formData.get('military_number'),
        full_name: formData.get('full_name'),
        rank: formData.get('rank'),
        specialization: formData.get('specialization'),
        unit: formData.get('unit'),
        service_years: formData.get('service_years'),
        notes: formData.get('notes') || ''
    };
    
    // Show loading
    const submitBtn = form.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
    
    // Simulate API call
    setTimeout(() => {
        if (savePersonnelData(personnelData)) {
            showToast('تم حفظ بيانات الفرد بنجاح', 'success');
            form.reset();
            form.classList.remove('was-validated');
            updatePersonnelTable();
        } else {
            showToast('حدث خطأ أثناء حفظ البيانات', 'error');
        }
        hideLoading();
    }, 1500);
}

function savePersonnelData(data) {
    try {
        // Generate new ID
        const newId = Math.max(...personnelData.map(p => p.id), 0) + 1;
        
        // Add new personnel
        const newPersonnel = {
            id: newId,
            ...data,
            status: 'قيد المراجعة',
            created_at: new Date().toISOString().split('T')[0]
        };
        
        personnelData.push(newPersonnel);
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        return false;
    }
}

// ===== Table Management =====
function updatePersonnelTable() {
    const tableBody = document.getElementById('personnelTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    personnelData.forEach(person => {
        const row = createPersonnelRow(person);
        tableBody.appendChild(row);
    });
    
    // Refresh DataTable if initialized
    if (dataTable) {
        dataTable.destroy();
        initializeDataTable();
    }
}

function createPersonnelRow(person) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${person.military_number}</td>
        <td>${person.full_name}</td>
        <td><span class="badge bg-primary">${person.rank}</span></td>
        <td>${person.specialization}</td>
        <td>${person.unit}</td>
        <td>${person.service_years}</td>
        <td>${getStatusBadge(person.status)}</td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="viewPersonnel(${person.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="editPersonnel(${person.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deletePersonnel(${person.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    // Add animation
    row.classList.add('slide-in-right');
    
    return row;
}

function getStatusBadge(status) {
    const statusMap = {
        'مؤهل للترقية': 'bg-success',
        'قيد المراجعة': 'bg-warning',
        'غير مؤهل': 'bg-danger',
        'مؤجل': 'bg-secondary'
    };
    
    const badgeClass = statusMap[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass}">${status}</span>`;
}

// ===== Personnel Actions =====
function viewPersonnel(id) {
    const person = personnelData.find(p => p.id === id);
    if (!person) {
        showToast('لم يتم العثور على بيانات الفرد', 'error');
        return;
    }
    
    currentPersonnelId = id;
    displayPersonnelDetails(person);
    
    const modal = new bootstrap.Modal(document.getElementById('personnelModal'));
    modal.show();
}

function editPersonnel(id) {
    const person = personnelData.find(p => p.id === id);
    if (!person) {
        showToast('لم يتم العثور على بيانات الفرد', 'error');
        return;
    }
    
    // Fill form with person data
    fillFormWithPersonnelData(person);
    
    // Scroll to form
    document.querySelector('#personnelForm').scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
    });
    
    showToast('تم تحميل بيانات الفرد للتعديل', 'success');
}

function deletePersonnel(id) {
    const person = personnelData.find(p => p.id === id);
    if (!person) {
        showToast('لم يتم العثور على بيانات الفرد', 'error');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف بيانات ${person.full_name}؟`)) {
        personnelData = personnelData.filter(p => p.id !== id);
        updatePersonnelTable();
        showToast('تم حذف بيانات الفرد بنجاح', 'success');
    }
}

function displayPersonnelDetails(person) {
    const modalBody = document.getElementById('personnelModalBody');
    modalBody.innerHTML = `
        <div class="personnel-details">
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الرقم العسكري:</span>
                <span class="personnel-detail-value">${person.military_number}</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الاسم الكامل:</span>
                <span class="personnel-detail-value">${person.full_name}</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الرتبة:</span>
                <span class="personnel-detail-value"><span class="badge bg-primary">${person.rank}</span></span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">التخصص:</span>
                <span class="personnel-detail-value">${person.specialization}</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الوحدة العسكرية:</span>
                <span class="personnel-detail-value">${person.unit}</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">سنوات الخدمة:</span>
                <span class="personnel-detail-value">${person.service_years} سنة</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الحالة:</span>
                <span class="personnel-detail-value">${getStatusBadge(person.status)}</span>
            </div>
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">تاريخ الإضافة:</span>
                <span class="personnel-detail-value">${formatDate(person.created_at)}</span>
            </div>
            ${person.notes ? `
            <div class="personnel-detail-item">
                <span class="personnel-detail-label">الملاحظات:</span>
                <span class="personnel-detail-value">${person.notes}</span>
            </div>
            ` : ''}
        </div>
    `;
}

function fillFormWithPersonnelData(person) {
    document.getElementById('military_number').value = person.military_number;
    document.getElementById('full_name').value = person.full_name;
    document.getElementById('rank').value = person.rank;
    document.getElementById('specialization').value = person.specialization;
    document.getElementById('unit').value = person.unit;
    document.getElementById('service_years').value = person.service_years;
    document.getElementById('notes').value = person.notes || '';
}

// ===== Search and Filter =====
function handleSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    
    if (dataTable) {
        dataTable.search(searchTerm).draw();
    }
}

function handleFilter() {
    const rankFilter = document.getElementById('rankFilter').value;
    const unitFilter = document.getElementById('unitFilter').value;
    
    if (dataTable) {
        // Apply filters
        dataTable.columns(2).search(rankFilter).draw();
        dataTable.columns(4).search(unitFilter).draw();
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('rankFilter').value = '';
    document.getElementById('unitFilter').value = '';
    
    if (dataTable) {
        dataTable.search('').columns().search('').draw();
    }
}

// ===== Export Functions =====
function exportPersonnelData() {
    const csvData = [
        ['الرقم العسكري', 'الاسم الكامل', 'الرتبة', 'التخصص', 'الوحدة', 'سنوات الخدمة', 'الحالة', 'الملاحظات']
    ];
    
    personnelData.forEach(person => {
        csvData.push([
            person.military_number,
            person.full_name,
            person.rank,
            person.specialization,
            person.unit,
            person.service_years,
            person.status,
            person.notes || ''
        ]);
    });
    
    exportToCSV(csvData, 'قائمة_الأفراد_العسكريين.csv');
    showToast('تم تصدير البيانات بنجاح', 'success');
}

function printPersonnelList() {
    window.print();
}

// ===== Utility Functions =====
function formatMilitaryNumber(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 8) {
        value = value.substring(0, 8);
    }
    e.target.value = value;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function editCurrentPersonnel() {
    if (currentPersonnelId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('personnelModal'));
        modal.hide();
        editPersonnel(currentPersonnelId);
    }
}

// ===== Validation Functions =====
function validateMilitaryNumber(number) {
    return /^\d{8}$/.test(number);
}

function validatePersonnelData(data) {
    const errors = [];
    
    if (!data.military_number || !validateMilitaryNumber(data.military_number)) {
        errors.push('الرقم العسكري يجب أن يكون 8 أرقام');
    }
    
    if (!data.full_name || data.full_name.trim().length < 3) {
        errors.push('الاسم الكامل يجب أن يكون 3 أحرف على الأقل');
    }
    
    if (!data.rank) {
        errors.push('يرجى اختيار الرتبة العسكرية');
    }
    
    if (!data.specialization) {
        errors.push('يرجى اختيار التخصص');
    }
    
    if (!data.unit) {
        errors.push('يرجى اختيار الوحدة العسكرية');
    }
    
    if (!data.service_years || data.service_years < 1 || data.service_years > 40) {
        errors.push('سنوات الخدمة يجب أن تكون بين 1 و 40 سنة');
    }
    
    return errors;
}

// ===== Auto-save functionality =====
function enableAutoSave() {
    const form = document.getElementById('personnelForm');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            const formData = new FormData(form);
            localStorage.setItem('personnel_form_draft', JSON.stringify(Object.fromEntries(formData)));
        });
    });
}

function loadDraft() {
    const draft = localStorage.getItem('personnel_form_draft');
    if (draft) {
        const data = JSON.parse(draft);
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = data[key];
            }
        });
    }
}

function clearDraft() {
    localStorage.removeItem('personnel_form_draft');
}

// Initialize auto-save when page loads
document.addEventListener('DOMContentLoaded', function() {
    enableAutoSave();
    loadDraft();
});
