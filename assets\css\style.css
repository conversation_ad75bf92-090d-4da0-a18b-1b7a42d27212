/* ===== Military Theme CSS ===== */

:root {
    --military-green: #2d4a2b;
    --military-dark-green: #1a2e19;
    --military-beige: #d4c5a0;
    --military-gold: #d4af37;
    --military-gray: #5a5a5a;
    --military-light-gray: #f5f5f5;
    --text-dark: #2c3e50;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.2);
    --shadow-heavy: rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--military-light-gray);
    overflow-x: hidden;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--military-dark-green);
}

.text-gold {
    color: var(--military-gold) !important;
}

.text-military {
    color: var(--military-green) !important;
}

/* ===== Navigation ===== */
.military-nav {
    background: linear-gradient(135deg, var(--military-dark-green) 0%, var(--military-green) 100%);
    box-shadow: 0 2px 20px var(--shadow-medium);
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.military-nav .navbar-brand {
    font-weight: 700;
    transition: transform 0.3s ease;
}

.military-nav .navbar-brand:hover {
    transform: scale(1.05);
}

.logo-img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px var(--shadow-light));
}

.military-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    margin: 0 0.25rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.military-nav .nav-link:hover,
.military-nav .nav-link.active {
    color: var(--military-gold) !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.military-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--military-gold);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.military-nav .nav-link:hover::before,
.military-nav .nav-link.active::before {
    width: 80%;
}

/* ===== Hero Section ===== */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--military-dark-green) 0%, var(--military-green) 50%, var(--military-beige) 100%);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="military-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,0 20,10 10,20 0,10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23military-pattern)"/></svg>');
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
}

.hero-logo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px var(--shadow-heavy));
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px var(--shadow-heavy);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #fff, var(--military-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.4rem;
    font-weight: 400;
    text-shadow: 1px 1px 2px var(--shadow-medium);
    opacity: 0.95;
    max-width: 800px;
    margin: 0 auto;
}

.hero-buttons {
    margin-top: 2rem;
}

/* ===== Buttons ===== */
.btn-military {
    background: linear-gradient(135deg, var(--military-gold) 0%, #b8941f 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-military:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    color: white;
}

.btn-military::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-military:hover::before {
    left: 100%;
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    font-weight: 600;
    padding: 10px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--military-gold);
    color: var(--military-gold);
    transform: translateY(-3px);
}

/* ===== Features Section ===== */
.features-section {
    background: white;
    position: relative;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--military-dark-green);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--military-gray);
    margin-bottom: 3rem;
}

.feature-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 25px var(--shadow-light);
    border: 1px solid rgba(45, 74, 43, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px var(--shadow-medium);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--military-green), var(--military-dark-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, var(--military-gold), #b8941f);
}

.feature-card h4 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--military-dark-green);
}

.feature-card p {
    color: var(--military-gray);
    line-height: 1.6;
}

/* ===== Statistics Section ===== */
.stats-section {
    background: linear-gradient(135deg, var(--military-green) 0%, var(--military-dark-green) 100%);
    color: white;
    position: relative;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stats-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stats-pattern)"/></svg>');
}

.stat-item {
    position: relative;
    z-index: 2;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--military-gold);
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px var(--shadow-heavy);
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
}

/* ===== Footer ===== */
.footer {
    background: var(--military-dark-green);
    color: white;
    padding: 2rem 0;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-logo {
        width: 80px;
        height: 80px;
    }
    
    .feature-card {
        margin-bottom: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* ===== Animation Classes ===== */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* ===== Loading Animation ===== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--military-gold);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== Utility Classes ===== */
.bg-military {
    background-color: var(--military-green) !important;
}

.bg-military-dark {
    background-color: var(--military-dark-green) !important;
}

.bg-military-beige {
    background-color: var(--military-beige) !important;
}

.border-military {
    border-color: var(--military-green) !important;
}

.shadow-military {
    box-shadow: 0 4px 15px var(--shadow-light) !important;
}
