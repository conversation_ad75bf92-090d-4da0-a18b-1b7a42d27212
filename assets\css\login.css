/* ===== Login Page Styles ===== */

.login-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow: hidden;
    position: relative;
    font-family: 'Cairo', sans-serif;
}

/* Background Animation */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--military-dark-green) 0%, var(--military-green) 50%, var(--military-beige) 100%);
    z-index: -2;
}

.military-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="military-bg" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><polygon points="20,0 40,20 20,40 0,20" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23military-bg)"/></svg>');
    opacity: 0.5;
    animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(40px) translateY(40px); }
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.floating-element:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    animation-duration: 6s;
}

.floating-element:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    animation-duration: 10s;
}

.floating-element:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1s;
    animation-duration: 7s;
}

.floating-element:nth-child(5) {
    top: 10%;
    left: 50%;
    animation-delay: 3s;
    animation-duration: 9s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.1;
    }
}

/* Login Container */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
}

/* Login Card */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 3rem 2.5rem;
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    max-width: 450px;
    width: 100%;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green), var(--military-gold));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Login Header */
.login-header {
    margin-bottom: 2.5rem;
}

.logo-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.login-logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    position: relative;
    z-index: 2;
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    to { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

.login-title {
    font-size: 2rem;
    font-weight: 900;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.login-subtitle {
    font-size: 1rem;
    color: var(--military-gray);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.title-divider {
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
    margin: 0 auto;
    border-radius: 2px;
}

/* Form Styles */
.login-form {
    position: relative;
}

.form-label {
    font-weight: 600;
    color: var(--military-dark-green);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
}

.input-group {
    margin-bottom: 0.5rem;
}

.input-group-text {
    background: linear-gradient(135deg, var(--military-green), var(--military-dark-green));
    color: white;
    border: none;
    border-radius: 12px 0 0 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control {
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 12px 12px 0;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.2rem rgba(45, 74, 43, 0.25);
    background: white;
}

.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l-.94-.94L4.5 8.5z'/%3e%3c/svg%3e");
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
}

/* Password Toggle */
#togglePassword {
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 12px 12px 0;
    background: rgba(248, 249, 250, 0.9);
    transition: all 0.3s ease;
}

#togglePassword:hover {
    background: var(--military-green);
    color: white;
    border-color: var(--military-green);
}

/* Captcha */
.captcha-container {
    background: rgba(248, 249, 250, 0.8);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.captcha-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 0.75rem;
}

#captchaText {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--military-dark-green);
    font-family: 'Courier New', monospace;
}

/* Login Button */
.btn-login {
    background: linear-gradient(135deg, var(--military-gold) 0%, #b8941f 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    color: white;
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:disabled {
    opacity: 0.7;
    transform: none;
    cursor: not-allowed;
}

/* Forgot Password Link */
.forgot-password-link {
    color: var(--military-green);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.forgot-password-link:hover {
    color: var(--military-gold);
    text-decoration: underline;
}

/* Security Notice */
.security-notice {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.security-icon {
    color: #dc3545;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.security-text {
    font-size: 0.85rem;
    color: var(--military-gray);
    line-height: 1.4;
}

/* Success Modal */
.success-icon i {
    font-size: 4rem;
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.loading-progress .progress {
    height: 8px;
    border-radius: 10px;
    overflow: hidden;
}

/* Login Footer */
.login-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem 0;
    font-size: 0.85rem;
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-card {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }
    
    .login-logo {
        width: 80px;
        height: 80px;
    }
    
    .logo-glow {
        width: 100px;
        height: 100px;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .login-subtitle {
        font-size: 0.9rem;
    }
    
    .floating-element {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .login-card {
        padding: 1.5rem 1rem;
        margin: 1rem;
    }
    
    .login-header {
        margin-bottom: 2rem;
    }
    
    .form-control,
    .input-group-text {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .btn-login {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .security-notice {
        padding: 0.75rem;
        font-size: 0.8rem;
    }
}

/* Animation Classes */
.login-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-success {
    animation: loginSuccess 0.6s ease-out;
}

@keyframes loginSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Loading States */
.btn-loading {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.btn-text {
    transition: opacity 0.3s ease;
}

.btn-login.loading .btn-text {
    opacity: 0;
}

.btn-login.loading .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Form Validation */
.was-validated .form-control:invalid {
    border-color: #dc3545;
}

.was-validated .form-control:valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(30, 30, 30, 0.95);
        color: #e9ecef;
    }
    
    .login-title {
        color: #e9ecef;
    }
    
    .form-control {
        background: rgba(40, 40, 40, 0.9);
        color: #e9ecef;
        border-color: #495057;
    }
    
    .form-control:focus {
        background: #495057;
        color: white;
    }
}
