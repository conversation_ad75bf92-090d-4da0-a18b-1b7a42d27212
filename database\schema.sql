-- ===== Military Promotion System Database Schema =====
-- نظام ترشيحات الترقيات العسكرية - قاعدة البيانات

-- Create database
CREATE DATABASE IF NOT EXISTS military_promotion_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE military_promotion_system;

-- ===== Users Table =====
-- جدول المستخدمين
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'officer', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== Military Units Table =====
-- جدول الوحدات العسكرية
CREATE TABLE military_units (
    id INT PRIMARY KEY AUTO_INCREMENT,
    unit_name VARCHAR(100) NOT NULL,
    unit_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    commander_name VARCHAR(100),
    location VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===== Military Ranks Table =====
-- جدول الرتب العسكرية
CREATE TABLE military_ranks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    rank_name VARCHAR(50) NOT NULL,
    rank_level INT NOT NULL,
    rank_category ENUM('enlisted', 'nco', 'officer') NOT NULL,
    promotion_requirements TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== Specializations Table =====
-- جدول التخصصات العسكرية
CREATE TABLE specializations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    specialization_name VARCHAR(100) NOT NULL,
    specialization_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    required_training TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== Personnel Table =====
-- جدول الأفراد العسكريين
CREATE TABLE personnel (
    id INT PRIMARY KEY AUTO_INCREMENT,
    military_number VARCHAR(20) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    rank_id INT NOT NULL,
    specialization_id INT NOT NULL,
    unit_id INT NOT NULL,
    service_years INT NOT NULL DEFAULT 0,
    date_of_birth DATE,
    enlistment_date DATE,
    national_id VARCHAR(20),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    emergency_contact VARCHAR(100),
    emergency_phone VARCHAR(20),
    notes TEXT,
    status ENUM('active', 'inactive', 'retired', 'transferred') DEFAULT 'active',
    promotion_eligible BOOLEAN DEFAULT FALSE,
    last_promotion_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (rank_id) REFERENCES military_ranks(id),
    FOREIGN KEY (specialization_id) REFERENCES specializations(id),
    FOREIGN KEY (unit_id) REFERENCES military_units(id),
    
    INDEX idx_military_number (military_number),
    INDEX idx_rank (rank_id),
    INDEX idx_unit (unit_id),
    INDEX idx_status (status)
);

-- ===== Exam Types Table =====
-- جدول أنواع الاختبارات
CREATE TABLE exam_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    exam_name VARCHAR(100) NOT NULL,
    exam_code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    max_score DECIMAL(5,2) DEFAULT 100.00,
    passing_score DECIMAL(5,2) DEFAULT 60.00,
    weight_percentage DECIMAL(5,2) DEFAULT 25.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===== Exams Table =====
-- جدول الاختبارات
CREATE TABLE exams (
    id INT PRIMARY KEY AUTO_INCREMENT,
    exam_name VARCHAR(100) NOT NULL,
    exam_date DATE NOT NULL,
    exam_location VARCHAR(100),
    description TEXT,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_exam_date (exam_date),
    INDEX idx_status (status)
);

-- ===== Grades Table =====
-- جدول الدرجات
CREATE TABLE grades (
    id INT PRIMARY KEY AUTO_INCREMENT,
    personnel_id INT NOT NULL,
    exam_id INT NOT NULL,
    general_course DECIMAL(5,2) DEFAULT 0.00,
    specialization_course DECIMAL(5,2) DEFAULT 0.00,
    physical_education DECIMAL(5,2) DEFAULT 0.00,
    military_movements DECIMAL(5,2) DEFAULT 0.00,
    total_score DECIMAL(6,2) GENERATED ALWAYS AS (
        general_course + specialization_course + physical_education + military_movements
    ) STORED,
    average_score DECIMAL(5,2) GENERATED ALWAYS AS (
        (general_course + specialization_course + physical_education + military_movements) / 4
    ) STORED,
    grade_status ENUM('excellent', 'very_good', 'good', 'acceptable', 'failed') 
        GENERATED ALWAYS AS (
            CASE 
                WHEN (general_course + specialization_course + physical_education + military_movements) / 4 >= 90 THEN 'excellent'
                WHEN (general_course + specialization_course + physical_education + military_movements) / 4 >= 80 THEN 'very_good'
                WHEN (general_course + specialization_course + physical_education + military_movements) / 4 >= 70 THEN 'good'
                WHEN (general_course + specialization_course + physical_education + military_movements) / 4 >= 60 THEN 'acceptable'
                ELSE 'failed'
            END
        ) STORED,
    promotion_eligible BOOLEAN GENERATED ALWAYS AS (
        (general_course + specialization_course + physical_education + military_movements) / 4 >= 75
    ) STORED,
    exam_date DATE NOT NULL,
    notes TEXT,
    entered_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (personnel_id) REFERENCES personnel(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES exams(id),
    FOREIGN KEY (entered_by) REFERENCES users(id),
    
    UNIQUE KEY unique_personnel_exam (personnel_id, exam_id),
    INDEX idx_personnel (personnel_id),
    INDEX idx_exam_date (exam_date),
    INDEX idx_grade_status (grade_status),
    INDEX idx_promotion_eligible (promotion_eligible)
);

-- ===== Promotion Recommendations Table =====
-- جدول توصيات الترقية
CREATE TABLE promotion_recommendations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    personnel_id INT NOT NULL,
    current_rank_id INT NOT NULL,
    recommended_rank_id INT NOT NULL,
    recommendation_date DATE NOT NULL,
    justification TEXT NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'under_review') DEFAULT 'pending',
    reviewed_by INT,
    review_date DATE,
    review_notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (personnel_id) REFERENCES personnel(id) ON DELETE CASCADE,
    FOREIGN KEY (current_rank_id) REFERENCES military_ranks(id),
    FOREIGN KEY (recommended_rank_id) REFERENCES military_ranks(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    
    INDEX idx_personnel (personnel_id),
    INDEX idx_status (status),
    INDEX idx_recommendation_date (recommendation_date)
);

-- ===== Audit Log Table =====
-- جدول سجل العمليات
CREATE TABLE audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- ===== Insert Default Data =====

-- Insert default military ranks
INSERT INTO military_ranks (rank_name, rank_level, rank_category) VALUES
('جندي', 1, 'enlisted'),
('جندي أول', 2, 'enlisted'),
('عريف', 3, 'enlisted'),
('وكيل رقيب', 4, 'nco'),
('رقيب', 5, 'nco'),
('رقيب أول', 6, 'nco'),
('رئيس رقباء', 7, 'nco'),
('ملازم', 8, 'officer'),
('ملازم أول', 9, 'officer'),
('نقيب', 10, 'officer'),
('رائد', 11, 'officer'),
('مقدم', 12, 'officer'),
('عقيد', 13, 'officer'),
('عميد', 14, 'officer');

-- Insert default military units
INSERT INTO military_units (unit_name, unit_code, description) VALUES
('الحرس الملكي', 'RG001', 'الحرس الملكي السعودي'),
('القوات البرية', 'LA001', 'القوات البرية السعودية'),
('القوات الجوية', 'AF001', 'القوات الجوية الملكية السعودية'),
('القوات البحرية', 'NF001', 'القوات البحرية الملكية السعودية'),
('قوات الدفاع الجوي', 'AD001', 'قوات الدفاع الجوي الملكية السعودية'),
('الحرس الوطني', 'NG001', 'الحرس الوطني السعودي'),
('قوات الأمن الخاصة', 'SF001', 'قوات الأمن الخاصة'),
('القوات الخاصة', 'SOF001', 'القوات الخاصة السعودية'),
('الشؤون الطبية', 'MED001', 'الشؤون الطبية العسكرية'),
('الخدمات اللوجستية', 'LOG001', 'الخدمات اللوجستية العسكرية');

-- Insert default specializations
INSERT INTO specializations (specialization_name, specialization_code, description) VALUES
('مشاة', 'INF', 'تخصص المشاة'),
('مدرع', 'ARM', 'تخصص المدرعات'),
('مدفعية', 'ART', 'تخصص المدفعية'),
('هندسة', 'ENG', 'تخصص الهندسة العسكرية'),
('إشارة', 'SIG', 'تخصص الإشارة والاتصالات'),
('تموين', 'SUP', 'تخصص التموين'),
('نقل', 'TRA', 'تخصص النقل'),
('طبي', 'MED', 'تخصص طبي'),
('أمن', 'SEC', 'تخصص الأمن'),
('استخبارات', 'INT', 'تخصص الاستخبارات'),
('طيران', 'AVI', 'تخصص الطيران'),
('بحري', 'NAV', 'تخصص بحري');

-- Insert default exam types
INSERT INTO exam_types (exam_name, exam_code, description, max_score, weight_percentage) VALUES
('المقرر العام', 'GEN', 'اختبار المقرر العام', 100.00, 25.00),
('التخصص', 'SPEC', 'اختبار التخصص', 100.00, 25.00),
('التربية البدنية', 'PHY', 'اختبار التربية البدنية', 100.00, 25.00),
('حركات المشاة', 'MIL', 'اختبار حركات المشاة', 100.00, 25.00);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password_hash, email, full_name, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'مدير النظام', 'admin');

-- Create views for reporting
CREATE VIEW personnel_summary AS
SELECT 
    p.id,
    p.military_number,
    p.full_name,
    r.rank_name,
    s.specialization_name,
    u.unit_name,
    p.service_years,
    p.status,
    p.promotion_eligible,
    COALESCE(g.average_score, 0) as latest_average,
    COALESCE(g.grade_status, 'not_tested') as latest_grade_status
FROM personnel p
LEFT JOIN military_ranks r ON p.rank_id = r.id
LEFT JOIN specializations s ON p.specialization_id = s.id
LEFT JOIN military_units u ON p.unit_id = u.id
LEFT JOIN grades g ON p.id = g.personnel_id 
    AND g.exam_date = (
        SELECT MAX(exam_date) 
        FROM grades 
        WHERE personnel_id = p.id
    );

-- Create indexes for better performance
CREATE INDEX idx_personnel_summary_rank ON personnel_summary(rank_name);
CREATE INDEX idx_personnel_summary_unit ON personnel_summary(unit_name);
CREATE INDEX idx_personnel_summary_status ON personnel_summary(status);
CREATE INDEX idx_personnel_summary_eligible ON personnel_summary(promotion_eligible);
