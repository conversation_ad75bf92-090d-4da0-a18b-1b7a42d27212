# نظام ترشيحات الترقيات العسكرية
## Military Promotion Nomination System

### 🎖️ نظرة عامة | Overview

نظام ويب احترافي متكامل لإدارة ترشيحات الأفراد العسكريين للترقية، مصمم خصيصاً لوزارة الدفاع السعودية بمظهر رسمي مستلهم من التصميم العسكري.

A comprehensive professional web system for managing military personnel promotion nominations, specifically designed for the Saudi Ministry of Defense with an official military-inspired design.

### ✨ المميزات الرئيسية | Key Features

#### 🏠 الصفحة الرئيسية | Home Page
- واجهة ترحيبية رسمية مع شعار وزارة الدفاع
- تصميم بالألوان العسكرية (الأخضر الداكن، البيج، الذهبي)
- إحصائيات تفاعلية ومؤشرات الأداء
- تأثيرات بصرية احترافية وأنيميشن فخم

#### 👥 إدارة الأفراد العسكريين | Personnel Management
- نموذج شامل لإدخال بيانات الأفراد المرشحين للترقية
- جدول تفاعلي لعرض جميع الأفراد مع وظائف البحث والتصفية
- إمكانية التعديل والحذف والعرض التفصيلي
- تصفية حسب الرتبة والوحدة العسكرية

#### 📊 رصد الدرجات | Grades Monitoring
- نظام دقيق لرصد درجات الاختبارات العسكرية
- حساب تلقائي للمجموع والمعدل العام
- مؤشرات لونية للدرجات (أخضر للناجح، أصفر للضعيف، أحمر للراسب)
- تقييم أهلية الترقية تلقائياً

#### 📈 التقارير والإحصائيات | Reports & Statistics
- تقارير شاملة قابلة للتصدير (PDF, Excel, CSV)
- مخططات بيانية تفاعلية
- إحصائيات مفصلة حسب الوحدات والرتب
- تقارير مخصصة حسب الفترة الزمنية

#### 🔐 نظام الأمان | Security System
- تسجيل دخول آمن مع تشفير كلمات المرور
- نظام التحقق الأمني (Captcha)
- حماية من محاولات الاختراق
- تسجيل جميع العمليات للمراجعة

### 🛠️ التقنيات المستخدمة | Technologies Used

#### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **Bootstrap 5 RTL** - إطار العمل المتجاوب
- **JavaScript ES6+** - التفاعل والديناميكية
- **Chart.js** - المخططات البيانية
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

#### Backend
- **PHP 8.0+** - لغة البرمجة الخلفية
- **MySQL 8.0+** - قاعدة البيانات
- **PDO** - الاتصال الآمن بقاعدة البيانات
- **RESTful API** - واجهات برمجة التطبيقات

#### Libraries & Tools
- **jsPDF** - إنشاء ملفات PDF
- **DataTables** - جداول البيانات التفاعلية
- **Animate.css** - التأثيرات البصرية

### 📁 هيكل المشروع | Project Structure

```
الترقيات_رصد/
├── index.html                 # الصفحة الرئيسية
├── personnel.html             # إدارة الأفراد
├── grades.html               # رصد الدرجات
├── reports.html              # التقارير
├── login.html                # تسجيل الدخول
├── assets/
│   ├── css/
│   │   ├── style.css         # التصميم الرئيسي
│   │   ├── personnel.css     # تصميم صفحة الأفراد
│   │   ├── grades.css        # تصميم صفحة الدرجات
│   │   ├── reports.css       # تصميم صفحة التقارير
│   │   └── login.css         # تصميم صفحة الدخول
│   ├── js/
│   │   ├── main.js           # الوظائف الرئيسية
│   │   ├── personnel.js      # وظائف إدارة الأفراد
│   │   ├── grades.js         # وظائف رصد الدرجات
│   │   ├── reports.js        # وظائف التقارير
│   │   └── login.js          # وظائف تسجيل الدخول
│   └── images/
│       └── mod-logo.svg      # شعار وزارة الدفاع
├── api/
│   ├── config/
│   │   └── database.php      # إعدادات قاعدة البيانات
│   └── personnel.php         # API إدارة الأفراد
├── database/
│   └── schema.sql            # هيكل قاعدة البيانات
└── README.md                 # دليل المشروع
```

### 🚀 التثبيت والإعداد | Installation & Setup

#### متطلبات النظام | System Requirements
- **Web Server**: Apache 2.4+ أو Nginx 1.18+
- **PHP**: 8.0 أو أحدث
- **MySQL**: 8.0 أو أحدث
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+

#### خطوات التثبيت | Installation Steps

1. **استنساخ المشروع | Clone Project**
```bash
git clone [repository-url]
cd الترقيات_رصد
```

2. **إعداد قاعدة البيانات | Database Setup**
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE military_promotion_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p military_promotion_system < database/schema.sql
```

3. **تكوين الاتصال | Configure Connection**
```php
// تحديث ملف api/config/database.php
private $host = "localhost";
private $db_name = "military_promotion_system";
private $username = "your_username";
private $password = "your_password";
```

4. **تشغيل الخادم | Start Server**
```bash
# باستخدام PHP المدمج
php -S localhost:8000

# أو رفع الملفات إلى خادم الويب
```

### 👤 بيانات الدخول الافتراضية | Default Login Credentials

```
اسم المستخدم | Username: admin
كلمة المرور | Password: admin123
```

### 🎨 التصميم والألوان | Design & Colors

#### نظام الألوان | Color Palette
- **الأخضر العسكري**: `#2d4a2b` (الأساسي)
- **الأخضر الداكن**: `#1a2e19` (الثانوي)
- **الذهبي**: `#d4af37` (التمييز)
- **البيج العسكري**: `#d4c5a0` (المساعد)
- **الرمادي**: `#5a5a5a` (النصوص)

#### الخطوط | Typography
- **الخط الأساسي**: Cairo (Google Fonts)
- **الأوزان**: 300, 400, 600, 700, 900

### 📱 التوافق والاستجابة | Compatibility & Responsiveness

- ✅ **Desktop**: 1920px وما فوق
- ✅ **Laptop**: 1366px - 1919px
- ✅ **Tablet**: 768px - 1365px
- ✅ **Mobile**: 320px - 767px
- ✅ **RTL Support**: دعم كامل للغة العربية

### 🔒 الأمان | Security Features

- **تشفير كلمات المرور**: bcrypt hashing
- **حماية SQL Injection**: Prepared statements
- **التحقق من المدخلات**: Input validation & sanitization
- **جلسات آمنة**: Secure session management
- **تسجيل العمليات**: Comprehensive audit trail

### 📊 قاعدة البيانات | Database Schema

#### الجداول الرئيسية | Main Tables
- `personnel` - بيانات الأفراد العسكريين
- `grades` - درجات الاختبارات
- `military_ranks` - الرتب العسكرية
- `military_units` - الوحدات العسكرية
- `specializations` - التخصصات العسكرية
- `users` - المستخدمين
- `audit_log` - سجل العمليات

### 🔧 التخصيص | Customization

#### إضافة رتب جديدة | Adding New Ranks
```sql
INSERT INTO military_ranks (rank_name, rank_level, rank_category) 
VALUES ('الرتبة الجديدة', 15, 'officer');
```

#### إضافة وحدات عسكرية | Adding Military Units
```sql
INSERT INTO military_units (unit_name, unit_code, description) 
VALUES ('الوحدة الجديدة', 'NEW001', 'وصف الوحدة');
```

### 🐛 استكشاف الأخطاء | Troubleshooting

#### مشاكل شائعة | Common Issues

1. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من بيانات الاتصال في `api/config/database.php`
   - تأكد من تشغيل خدمة MySQL

2. **مشاكل في الترميز العربي**
   - تأكد من استخدام `utf8mb4` في قاعدة البيانات
   - تحقق من إعدادات الخادم

3. **مشاكل في التصدير**
   - تأكد من تفعيل JavaScript في المتصفح
   - تحقق من أذونات الكتابة في المجلد

### 📞 الدعم والمساعدة | Support & Help

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-11-xxxxxxx
- 🌐 الموقع الرسمي: www.mod.gov.sa

### 📄 الترخيص | License

هذا المشروع مطور خصيصاً لوزارة الدفاع السعودية وجميع الحقوق محفوظة.

---

**© 2024 وزارة الدفاع - المملكة العربية السعودية**
**Ministry of Defense - Kingdom of Saudi Arabia**
