<?php
/**
 * Military Promotion System - Personnel API
 * نظام ترشيحات الترقيات العسكرية - واجهة برمجة التطبيقات للأفراد
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

class PersonnelAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    /**
     * Handle API requests
     * معالجة طلبات API
     */
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));
        
        // Get personnel ID if provided
        $personnelId = isset($pathParts[2]) ? intval($pathParts[2]) : null;

        try {
            switch ($method) {
                case 'GET':
                    if ($personnelId) {
                        $this->getPersonnel($personnelId);
                    } else {
                        $this->getAllPersonnel();
                    }
                    break;

                case 'POST':
                    $this->createPersonnel();
                    break;

                case 'PUT':
                    if ($personnelId) {
                        $this->updatePersonnel($personnelId);
                    } else {
                        $this->sendError('Personnel ID required for update', 400);
                    }
                    break;

                case 'DELETE':
                    if ($personnelId) {
                        $this->deletePersonnel($personnelId);
                    } else {
                        $this->sendError('Personnel ID required for delete', 400);
                    }
                    break;

                default:
                    $this->sendError('Method not allowed', 405);
                    break;
            }
        } catch (Exception $e) {
            $this->sendError('Internal server error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get all personnel
     * الحصول على جميع الأفراد
     */
    private function getAllPersonnel() {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;

        // Build filters
        $where = ['p.status = :status'];
        $params = ['status' => 'active'];

        if (isset($_GET['rank']) && !empty($_GET['rank'])) {
            $where[] = 'r.rank_name = :rank';
            $params['rank'] = $_GET['rank'];
        }

        if (isset($_GET['unit']) && !empty($_GET['unit'])) {
            $where[] = 'u.unit_name = :unit';
            $params['unit'] = $_GET['unit'];
        }

        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $where[] = '(p.full_name LIKE :search OR p.military_number LIKE :search)';
            $params['search'] = '%' . $_GET['search'] . '%';
        }

        $whereClause = implode(' AND ', $where);

        // Get total count
        $countQuery = "
            SELECT COUNT(*) as total
            FROM personnel p
            LEFT JOIN military_ranks r ON p.rank_id = r.id
            LEFT JOIN military_units u ON p.unit_id = u.id
            WHERE {$whereClause}
        ";
        $totalResult = $this->db->fetchOne($countQuery, $params);
        $total = $totalResult['total'];

        // Get personnel data
        $query = "
            SELECT 
                p.id,
                p.military_number,
                p.full_name,
                r.rank_name,
                s.specialization_name,
                u.unit_name,
                p.service_years,
                p.status,
                p.promotion_eligible,
                p.notes,
                p.created_at,
                COALESCE(g.average_score, 0) as latest_average,
                COALESCE(g.grade_status, 'not_tested') as latest_grade_status
            FROM personnel p
            LEFT JOIN military_ranks r ON p.rank_id = r.id
            LEFT JOIN specializations s ON p.specialization_id = s.id
            LEFT JOIN military_units u ON p.unit_id = u.id
            LEFT JOIN grades g ON p.id = g.personnel_id 
                AND g.exam_date = (
                    SELECT MAX(exam_date) 
                    FROM grades 
                    WHERE personnel_id = p.id
                )
            WHERE {$whereClause}
            ORDER BY p.created_at DESC
            LIMIT {$limit} OFFSET {$offset}
        ";

        $personnel = $this->db->fetchAll($query, $params);

        $this->sendResponse([
            'success' => true,
            'data' => $personnel,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }

    /**
     * Get single personnel
     * الحصول على فرد واحد
     */
    private function getPersonnel($id) {
        $query = "
            SELECT 
                p.*,
                r.rank_name,
                s.specialization_name,
                u.unit_name,
                u.unit_code
            FROM personnel p
            LEFT JOIN military_ranks r ON p.rank_id = r.id
            LEFT JOIN specializations s ON p.specialization_id = s.id
            LEFT JOIN military_units u ON p.unit_id = u.id
            WHERE p.id = :id
        ";

        $personnel = $this->db->fetchOne($query, ['id' => $id]);

        if (!$personnel) {
            $this->sendError('Personnel not found', 404);
            return;
        }

        // Get latest grades
        $gradesQuery = "
            SELECT * FROM grades 
            WHERE personnel_id = :personnel_id 
            ORDER BY exam_date DESC 
            LIMIT 1
        ";
        $latestGrades = $this->db->fetchOne($gradesQuery, ['personnel_id' => $id]);

        $personnel['latest_grades'] = $latestGrades;

        $this->sendResponse([
            'success' => true,
            'data' => $personnel
        ]);
    }

    /**
     * Create new personnel
     * إنشاء فرد جديد
     */
    private function createPersonnel() {
        $input = json_decode(file_get_contents('php://input'), true);

        // Validate required fields
        $required = ['military_number', 'full_name', 'rank_id', 'specialization_id', 'unit_id', 'service_years'];
        $missing = $this->db->validateRequired($input, $required);

        if (!empty($missing)) {
            $this->sendError('Missing required fields: ' . implode(', ', $missing), 400);
            return;
        }

        // Validate military number format
        if (!DatabaseUtils::validateMilitaryNumber($input['military_number'])) {
            $this->sendError('Invalid military number format', 400);
            return;
        }

        // Check if military number already exists
        if ($this->db->exists('personnel', 'military_number = ?', [$input['military_number']])) {
            $this->sendError('Military number already exists', 409);
            return;
        }

        // Sanitize input
        $data = [
            'military_number' => $this->db->sanitize($input['military_number']),
            'full_name' => $this->db->sanitize($input['full_name']),
            'rank_id' => intval($input['rank_id']),
            'specialization_id' => intval($input['specialization_id']),
            'unit_id' => intval($input['unit_id']),
            'service_years' => intval($input['service_years']),
            'notes' => isset($input['notes']) ? $this->db->sanitize($input['notes']) : null,
            'status' => 'active'
        ];

        // Optional fields
        if (isset($input['date_of_birth'])) {
            $data['date_of_birth'] = DatabaseUtils::formatDate($input['date_of_birth']);
        }
        if (isset($input['enlistment_date'])) {
            $data['enlistment_date'] = DatabaseUtils::formatDate($input['enlistment_date']);
        }
        if (isset($input['phone'])) {
            $data['phone'] = $this->db->sanitize($input['phone']);
        }
        if (isset($input['email'])) {
            if (DatabaseUtils::validateEmail($input['email'])) {
                $data['email'] = $this->db->sanitize($input['email']);
            } else {
                $this->sendError('Invalid email format', 400);
                return;
            }
        }

        try {
            $this->db->beginTransaction();
            
            $personnelId = $this->db->insert('personnel', $data);
            
            // Log audit trail
            $this->db->logAudit(1, 'CREATE', 'personnel', $personnelId, null, $data);
            
            $this->db->commit();

            $this->sendResponse([
                'success' => true,
                'message' => 'Personnel created successfully',
                'data' => ['id' => $personnelId]
            ], 201);

        } catch (Exception $e) {
            $this->db->rollback();
            $this->sendError('Failed to create personnel: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update personnel
     * تحديث بيانات الفرد
     */
    private function updatePersonnel($id) {
        $input = json_decode(file_get_contents('php://input'), true);

        // Check if personnel exists
        $existing = $this->db->fetchOne('SELECT * FROM personnel WHERE id = ?', [$id]);
        if (!$existing) {
            $this->sendError('Personnel not found', 404);
            return;
        }

        // Build update data
        $data = [];
        $allowedFields = ['full_name', 'rank_id', 'specialization_id', 'unit_id', 'service_years', 'notes', 'phone', 'email', 'address'];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                if (in_array($field, ['full_name', 'notes', 'phone', 'address'])) {
                    $data[$field] = $this->db->sanitize($input[$field]);
                } elseif (in_array($field, ['rank_id', 'specialization_id', 'unit_id', 'service_years'])) {
                    $data[$field] = intval($input[$field]);
                } elseif ($field === 'email') {
                    if (DatabaseUtils::validateEmail($input[$field])) {
                        $data[$field] = $this->db->sanitize($input[$field]);
                    } else {
                        $this->sendError('Invalid email format', 400);
                        return;
                    }
                }
            }
        }

        if (empty($data)) {
            $this->sendError('No valid fields to update', 400);
            return;
        }

        try {
            $this->db->beginTransaction();
            
            $updated = $this->db->update('personnel', $data, 'id = :id', ['id' => $id]);
            
            if ($updated > 0) {
                // Log audit trail
                $this->db->logAudit(1, 'UPDATE', 'personnel', $id, $existing, $data);
                
                $this->db->commit();
                
                $this->sendResponse([
                    'success' => true,
                    'message' => 'Personnel updated successfully'
                ]);
            } else {
                $this->db->rollback();
                $this->sendError('No changes made', 400);
            }

        } catch (Exception $e) {
            $this->db->rollback();
            $this->sendError('Failed to update personnel: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete personnel
     * حذف الفرد
     */
    private function deletePersonnel($id) {
        // Check if personnel exists
        $existing = $this->db->fetchOne('SELECT * FROM personnel WHERE id = ?', [$id]);
        if (!$existing) {
            $this->sendError('Personnel not found', 404);
            return;
        }

        try {
            $this->db->beginTransaction();
            
            // Soft delete - update status instead of actual deletion
            $deleted = $this->db->update('personnel', ['status' => 'inactive'], 'id = :id', ['id' => $id]);
            
            if ($deleted > 0) {
                // Log audit trail
                $this->db->logAudit(1, 'DELETE', 'personnel', $id, $existing, ['status' => 'inactive']);
                
                $this->db->commit();
                
                $this->sendResponse([
                    'success' => true,
                    'message' => 'Personnel deleted successfully'
                ]);
            } else {
                $this->db->rollback();
                $this->sendError('Failed to delete personnel', 500);
            }

        } catch (Exception $e) {
            $this->db->rollback();
            $this->sendError('Failed to delete personnel: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Send JSON response
     * إرسال استجابة JSON
     */
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit();
    }

    /**
     * Send error response
     * إرسال استجابة خطأ
     */
    private function sendError($message, $statusCode = 400) {
        http_response_code($statusCode);
        echo json_encode([
            'success' => false,
            'error' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// Initialize and handle request
$api = new PersonnelAPI();
$api->handleRequest();
?>
