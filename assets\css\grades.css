/* ===== Grades Management Styles ===== */

/* Grade Input Styles */
.grade-input {
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
}

.grade-input:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.2rem rgba(45, 74, 43, 0.25);
    transform: scale(1.02);
}

.grade-input.grade-excellent {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.grade-input.grade-good {
    border-color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

.grade-input.grade-average {
    border-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.grade-input.grade-poor {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* Grade Indicators */
.grade-indicator {
    margin-top: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 24px;
}

.grade-indicator.excellent {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.grade-indicator.good {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
}

.grade-indicator.average {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.grade-indicator.poor {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

.grade-indicator.empty {
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
    color: #6c757d;
}

/* Grade Summary Card */
.grade-summary-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid var(--military-green);
    border-radius: 15px;
    padding: 2rem;
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
}

.grade-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--military-gold), var(--military-green));
}

.summary-item {
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.summary-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.summary-value {
    font-size: 2rem;
    font-weight: 900;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.summary-label {
    font-size: 0.9rem;
    color: var(--military-gray);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Grade Badges in Table */
.grade-badge {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    min-width: 50px;
    text-align: center;
}

.grade-badge.grade-excellent {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.grade-badge.grade-good {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.grade-badge.grade-average {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.grade-badge.grade-poor {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* Grade Status Badges */
.badge.bg-excellent {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.badge.bg-very-good {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

.badge.bg-good {
    background: linear-gradient(135deg, #007bff, #6610f2) !important;
}

.badge.bg-acceptable {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    color: #212529 !important;
}

.badge.bg-failed {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

/* Grade Chart Container */
.grade-chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
}

.chart-title {
    text-align: center;
    color: var(--military-dark-green);
    font-weight: 700;
    margin-bottom: 2rem;
}

/* Progress Bars for Grades */
.grade-progress {
    margin: 1rem 0;
}

.grade-progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--military-dark-green);
}

.progress {
    height: 12px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.8s ease;
    border-radius: 10px;
}

.progress-bar.bg-excellent {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.progress-bar.bg-good {
    background: linear-gradient(90deg, #17a2b8, #6f42c1);
}

.progress-bar.bg-average {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-bar.bg-poor {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

/* Grade Statistics Cards */
.grade-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.grade-stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--military-green);
}

.grade-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.grade-stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--military-green), var(--military-dark-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.grade-stat-value {
    font-size: 2rem;
    font-weight: 900;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
}

.grade-stat-label {
    font-size: 0.9rem;
    color: var(--military-gray);
    font-weight: 600;
}

/* Grade Details Modal */
.grade-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.grade-detail-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    border-left: 4px solid var(--military-green);
}

.grade-detail-subject {
    font-weight: 600;
    color: var(--military-dark-green);
    margin-bottom: 0.5rem;
}

.grade-detail-score {
    font-size: 1.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.grade-detail-status {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Responsive Design */
@media (max-width: 768px) {
    .grade-summary-card {
        padding: 1rem;
    }
    
    .summary-value {
        font-size: 1.5rem;
    }
    
    .grade-badge {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
        min-width: 40px;
    }
    
    .grade-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .grade-stat-card {
        padding: 1rem;
    }
    
    .grade-stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .grade-stat-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .grade-input {
        font-size: 1rem;
    }
    
    .grade-summary-card {
        padding: 0.75rem;
    }
    
    .summary-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .summary-value {
        font-size: 1.2rem;
    }
    
    .grade-detail-grid {
        grid-template-columns: 1fr;
    }
    
    .table {
        font-size: 0.75rem;
    }
    
    .grade-badge {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
        min-width: 35px;
    }
}

/* Animation Classes */
.grade-animate-in {
    animation: gradeSlideIn 0.5s ease-out;
}

@keyframes gradeSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.grade-pulse {
    animation: gradePulse 2s infinite;
}

@keyframes gradePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(45, 74, 43, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(45, 74, 43, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(45, 74, 43, 0);
    }
}

/* Print Styles */
@media print {
    .grade-summary-card {
        background: white !important;
        border: 2px solid #000 !important;
    }
    
    .grade-badge {
        background: #f8f9fa !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }
    
    .summary-item {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .btn,
    .card-tools {
        display: none !important;
    }
}

/* Loading States */
.grade-loading {
    position: relative;
    overflow: hidden;
}

.grade-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: gradeShimmer 1.5s infinite;
}

@keyframes gradeShimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Grade Validation States */
.grade-input.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l-.94-.94L4.5 8.5z'/%3e%3c/svg%3e");
}

.grade-input.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
}

/* Success Animation */
.grade-success {
    animation: gradeSuccess 0.6s ease-out;
}

@keyframes gradeSuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
