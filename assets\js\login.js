// ===== Login Page JavaScript =====

// Global variables
let captchaAnswer = 0;
let loginAttempts = 0;
const MAX_LOGIN_ATTEMPTS = 3;
const LOCKOUT_TIME = 300000; // 5 minutes in milliseconds

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeLoginPage();
    setupLoginEventListeners();
    generateCaptcha();
    checkLockoutStatus();
});

// ===== Initialization Functions =====
function initializeLoginPage() {
    console.log('تم تحميل صفحة تسجيل الدخول');
    
    // Add entrance animations
    setTimeout(() => {
        document.querySelector('.login-card').classList.add('animate__animated', 'animate__fadeInUp');
    }, 500);
    
    // Focus on username field
    setTimeout(() => {
        document.getElementById('username').focus();
    }, 1000);
    
    // Load saved username if "remember me" was checked
    loadSavedCredentials();
}

function setupLoginEventListeners() {
    // Form submission
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLoginSubmit);
    }
    
    // Password toggle
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', togglePasswordVisibility);
    }
    
    // Captcha refresh
    const refreshCaptcha = document.getElementById('refreshCaptcha');
    if (refreshCaptcha) {
        refreshCaptcha.addEventListener('click', generateCaptcha);
    }
    
    // Real-time validation
    const inputs = document.querySelectorAll('#loginForm input');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
    
    // Forgot password form
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Prevent right-click and F12 for security
    document.addEventListener('contextmenu', e => e.preventDefault());
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
            e.preventDefault();
            showSecurityAlert();
        }
    });
}

// ===== Captcha Functions =====
function generateCaptcha() {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const operators = ['+', '-', '*'];
    const operator = operators[Math.floor(Math.random() * operators.length)];
    
    let question, answer;
    
    switch (operator) {
        case '+':
            question = `${num1} + ${num2} = ?`;
            answer = num1 + num2;
            break;
        case '-':
            question = `${Math.max(num1, num2)} - ${Math.min(num1, num2)} = ?`;
            answer = Math.max(num1, num2) - Math.min(num1, num2);
            break;
        case '*':
            const smallNum1 = Math.floor(Math.random() * 5) + 1;
            const smallNum2 = Math.floor(Math.random() * 5) + 1;
            question = `${smallNum1} × ${smallNum2} = ?`;
            answer = smallNum1 * smallNum2;
            break;
    }
    
    document.getElementById('captchaText').textContent = question;
    captchaAnswer = answer;
    
    // Clear captcha input
    document.getElementById('captchaInput').value = '';
    
    // Add refresh animation
    const refreshBtn = document.getElementById('refreshCaptcha');
    refreshBtn.style.transform = 'rotate(360deg)';
    setTimeout(() => {
        refreshBtn.style.transform = 'rotate(0deg)';
    }, 300);
}

// ===== Form Validation =====
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    switch (field.id) {
        case 'username':
            if (!value) {
                isValid = false;
                errorMessage = 'يرجى إدخال اسم المستخدم';
            } else if (value.length < 3) {
                isValid = false;
                errorMessage = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
            }
            break;
            
        case 'password':
            if (!value) {
                isValid = false;
                errorMessage = 'يرجى إدخال كلمة المرور';
            } else if (value.length < 6) {
                isValid = false;
                errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            }
            break;
            
        case 'captchaInput':
            const userAnswer = parseInt(value);
            if (isNaN(userAnswer) || userAnswer !== captchaAnswer) {
                isValid = false;
                errorMessage = 'الإجابة غير صحيحة';
            }
            break;
    }
    
    updateFieldValidation(field, isValid, errorMessage);
    return isValid;
}

function updateFieldValidation(field, isValid, errorMessage) {
    const feedbackElement = field.parentNode.querySelector('.invalid-feedback') || 
                           field.parentNode.parentNode.querySelector('.invalid-feedback');
    
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        if (feedbackElement) {
            feedbackElement.textContent = '';
        }
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        if (feedbackElement) {
            feedbackElement.textContent = errorMessage;
        }
    }
}

function clearFieldError(e) {
    const field = e.target;
    if (field.classList.contains('is-invalid') && field.value.trim()) {
        validateField(e);
    }
}

// ===== Login Handling =====
function handleLoginSubmit(e) {
    e.preventDefault();
    
    // Check if account is locked
    if (isAccountLocked()) {
        showLockoutMessage();
        return;
    }
    
    const form = e.target;
    const formData = new FormData(form);
    
    // Validate all fields
    const username = formData.get('username').trim();
    const password = formData.get('password');
    const captchaInput = parseInt(formData.get('captchaInput') || '0');
    
    let isFormValid = true;
    
    // Validate username
    if (!username || username.length < 3) {
        updateFieldValidation(document.getElementById('username'), false, 'اسم المستخدم غير صحيح');
        isFormValid = false;
    }
    
    // Validate password
    if (!password || password.length < 6) {
        updateFieldValidation(document.getElementById('password'), false, 'كلمة المرور غير صحيحة');
        isFormValid = false;
    }
    
    // Validate captcha
    if (captchaInput !== captchaAnswer) {
        updateFieldValidation(document.getElementById('captchaInput'), false, 'الإجابة غير صحيحة');
        isFormValid = false;
    }
    
    if (!isFormValid) {
        shakeLoginCard();
        return;
    }
    
    // Show loading state
    const loginBtn = document.getElementById('loginBtn');
    showLoginLoading(loginBtn);
    
    // Simulate authentication
    setTimeout(() => {
        authenticateUser(username, password, formData.get('rememberMe'));
    }, 2000);
}

function authenticateUser(username, password, rememberMe) {
    // Demo credentials - in real application, this would be server-side
    const validCredentials = [
        { username: 'admin', password: 'admin123' },
        { username: 'officer', password: 'officer123' },
        { username: 'commander', password: 'commander123' }
    ];
    
    const isValidUser = validCredentials.some(cred => 
        cred.username === username && cred.password === password
    );
    
    const loginBtn = document.getElementById('loginBtn');
    
    if (isValidUser) {
        // Successful login
        loginAttempts = 0;
        localStorage.removeItem('loginAttempts');
        localStorage.removeItem('lockoutTime');
        
        // Save credentials if remember me is checked
        if (rememberMe) {
            localStorage.setItem('rememberedUsername', username);
        } else {
            localStorage.removeItem('rememberedUsername');
        }
        
        // Show success
        hideLoginLoading(loginBtn);
        showLoginSuccess();
        
        // Redirect after delay
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 3000);
        
    } else {
        // Failed login
        loginAttempts++;
        localStorage.setItem('loginAttempts', loginAttempts.toString());
        
        hideLoginLoading(loginBtn);
        
        if (loginAttempts >= MAX_LOGIN_ATTEMPTS) {
            lockAccount();
        } else {
            showLoginError(MAX_LOGIN_ATTEMPTS - loginAttempts);
        }
        
        // Generate new captcha
        generateCaptcha();
        
        // Clear password field
        document.getElementById('password').value = '';
        
        // Shake card
        shakeLoginCard();
    }
}

// ===== UI Feedback Functions =====
function showLoginLoading(button) {
    button.classList.add('loading');
    button.disabled = true;
    button.querySelector('.btn-text').style.opacity = '0';
    button.querySelector('.btn-loading').classList.remove('d-none');
}

function hideLoginLoading(button) {
    button.classList.remove('loading');
    button.disabled = false;
    button.querySelector('.btn-text').style.opacity = '1';
    button.querySelector('.btn-loading').classList.add('d-none');
}

function shakeLoginCard() {
    const loginCard = document.querySelector('.login-card');
    loginCard.classList.add('login-shake');
    setTimeout(() => {
        loginCard.classList.remove('login-shake');
    }, 500);
}

function showLoginSuccess() {
    const loginCard = document.querySelector('.login-card');
    loginCard.classList.add('login-success');
    
    // Show success modal
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    successModal.show();
    
    // Animate progress bar
    const progressBar = document.querySelector('.progress-bar');
    let width = 0;
    const interval = setInterval(() => {
        width += 2;
        progressBar.style.width = width + '%';
        if (width >= 100) {
            clearInterval(interval);
        }
    }, 50);
}

function showLoginError(attemptsLeft) {
    const message = attemptsLeft > 0 
        ? `بيانات الدخول غير صحيحة. المحاولات المتبقية: ${attemptsLeft}`
        : 'تم تجاوز عدد المحاولات المسموحة';
    
    showToast(message, 'error');
}

// ===== Account Lockout Functions =====
function lockAccount() {
    const lockoutTime = Date.now() + LOCKOUT_TIME;
    localStorage.setItem('lockoutTime', lockoutTime.toString());
    showLockoutMessage();
}

function isAccountLocked() {
    const lockoutTime = localStorage.getItem('lockoutTime');
    if (!lockoutTime) return false;
    
    return Date.now() < parseInt(lockoutTime);
}

function checkLockoutStatus() {
    if (isAccountLocked()) {
        showLockoutMessage();
    } else {
        // Reset attempts if lockout has expired
        localStorage.removeItem('lockoutTime');
        localStorage.removeItem('loginAttempts');
        loginAttempts = 0;
    }
}

function showLockoutMessage() {
    const lockoutTime = localStorage.getItem('lockoutTime');
    const remainingTime = Math.ceil((parseInt(lockoutTime) - Date.now()) / 1000 / 60);
    
    showToast(`تم قفل الحساب مؤقتاً. المحاولة مرة أخرى خلال ${remainingTime} دقيقة`, 'error');
    
    // Disable form
    const form = document.getElementById('loginForm');
    const inputs = form.querySelectorAll('input, button');
    inputs.forEach(input => input.disabled = true);
}

// ===== Utility Functions =====
function togglePasswordVisibility() {
    const passwordField = document.getElementById('password');
    const toggleBtn = document.getElementById('togglePassword');
    const icon = toggleBtn.querySelector('i');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function loadSavedCredentials() {
    const rememberedUsername = localStorage.getItem('rememberedUsername');
    if (rememberedUsername) {
        document.getElementById('username').value = rememberedUsername;
        document.getElementById('rememberMe').checked = true;
        document.getElementById('password').focus();
    }
}

function handleKeyboardShortcuts(e) {
    // Enter key to submit form
    if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
        const form = document.getElementById('loginForm');
        if (form) {
            form.dispatchEvent(new Event('submit'));
        }
    }
    
    // Escape key to clear form
    if (e.key === 'Escape') {
        clearLoginForm();
    }
}

function clearLoginForm() {
    const form = document.getElementById('loginForm');
    form.reset();
    
    // Clear validation classes
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
        input.classList.remove('is-valid', 'is-invalid');
    });
    
    // Generate new captcha
    generateCaptcha();
    
    // Focus on username
    document.getElementById('username').focus();
}

function showSecurityAlert() {
    showToast('تم رصد محاولة وصول غير مصرح بها. سيتم تسجيل هذا النشاط.', 'error');
}

// ===== Forgot Password Functions =====
function handleForgotPassword(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const username = formData.get('resetUsername').trim();
    const email = formData.get('resetEmail').trim();
    
    if (!username || !email) {
        showToast('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    // Simulate password reset
    showToast('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
    modal.hide();
    
    // Clear form
    form.reset();
}

function resetPassword() {
    const form = document.getElementById('forgotPasswordForm');
    if (form.checkValidity()) {
        handleForgotPassword({ preventDefault: () => {}, target: form });
    } else {
        form.classList.add('was-validated');
    }
}

// ===== Security Functions =====
function logSecurityEvent(event, details) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        event: event,
        details: details,
        userAgent: navigator.userAgent,
        ip: 'CLIENT_IP' // Would be filled server-side
    };
    
    console.log('Security Event:', logEntry);
    // In real application, send to server
}

// ===== Session Management =====
function startSession(username) {
    const sessionData = {
        username: username,
        loginTime: new Date().toISOString(),
        sessionId: generateSessionId()
    };
    
    sessionStorage.setItem('userSession', JSON.stringify(sessionData));
    logSecurityEvent('LOGIN_SUCCESS', { username: username });
}

function generateSessionId() {
    return 'sess_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

// ===== Cleanup =====
window.addEventListener('beforeunload', function() {
    // Clear sensitive data
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.value = '';
    }
});
